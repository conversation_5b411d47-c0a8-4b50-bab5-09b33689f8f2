"""Neo4j client for DCMN framework."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from neo4j import GraphDatabase, Driver
from neo4j.exceptions import ServiceUnavailable, AuthError

from config.neo4j_config import Neo4jConfig


class Neo4jClient:
    """Neo4j database client for DCMN graph operations."""
    
    def __init__(self, config: Neo4jConfig):
        """Initialize Neo4j client."""
        self.config = config
        self.config.validate()
        self.logger = logging.getLogger(__name__)
        
        self.driver: Optional[Driver] = None
        self._connect()
    
    def _connect(self) -> None:
        """Establish connection to Neo4j database."""
        try:
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                **self.config.get_connection_config()
            )
            
            # Test connection
            with self.driver.session(database=self.config.database) as session:
                session.run("RETURN 1")
            
            self.logger.info(f"Connected to Neo4j at {self.config.uri}")
            
        except (ServiceUnavailable, AuthError) as e:
            self.logger.error(f"Failed to connect to Neo4j: {str(e)}")
            raise
    
    def close(self) -> None:
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            self.logger.info("Neo4j connection closed")
    
    def execute_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        database: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Execute a Cypher query and return results."""
        if not self.driver:
            raise RuntimeError("Neo4j driver not initialized")
        
        db_name = database or self.config.database
        
        try:
            with self.driver.session(database=db_name) as session:
                result = session.run(query, parameters or {})
                records = [record.data() for record in result]
                
            self.logger.debug(f"Query executed successfully, returned {len(records)} records")
            return records
            
        except Exception as e:
            self.logger.error(f"Query execution failed: {str(e)}")
            raise
    
    def execute_write_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute a write query and return summary."""
        if not self.driver:
            raise RuntimeError("Neo4j driver not initialized")
        
        db_name = database or self.config.database
        
        try:
            with self.driver.session(database=db_name) as session:
                result = session.run(query, parameters or {})
                summary = result.consume()
                
            self.logger.debug(f"Write query executed: {summary.counters}")
            return {
                'nodes_created': summary.counters.nodes_created,
                'nodes_deleted': summary.counters.nodes_deleted,
                'relationships_created': summary.counters.relationships_created,
                'relationships_deleted': summary.counters.relationships_deleted,
                'properties_set': summary.counters.properties_set
            }
            
        except Exception as e:
            self.logger.error(f"Write query execution failed: {str(e)}")
            raise
    
    def create_constraints(self) -> None:
        """Create database constraints for DCMN schema."""
        constraints = [
            "CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.user_id IS UNIQUE",
            "CREATE CONSTRAINT activity_name_unique IF NOT EXISTS FOR (a:Activity) REQUIRE a.name IS UNIQUE",
            "CREATE CONSTRAINT location_id_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.circle_id IS UNIQUE",
            "CREATE CONSTRAINT topic_id_unique IF NOT EXISTS FOR (t:Topic) REQUIRE t.topic_id IS UNIQUE",
            "CREATE CONSTRAINT regime_id_unique IF NOT EXISTS FOR (r:Regime) REQUIRE r.regime_id IS UNIQUE"
        ]
        
        for constraint in constraints:
            try:
                self.execute_write_query(constraint)
                self.logger.info(f"Created constraint: {constraint.split()[2]}")
            except Exception as e:
                self.logger.warning(f"Constraint creation failed: {str(e)}")
    
    def create_indexes(self) -> None:
        """Create database indexes for performance."""
        indexes = [
            "CREATE INDEX user_timestamp_idx IF NOT EXISTS FOR (u:User) ON (u.last_updated)",
            "CREATE INDEX activity_timestamp_idx IF NOT EXISTS FOR ()-[r:TRANSITIONS_TO]-() ON (r.timestamp)",
            "CREATE INDEX location_features_idx IF NOT EXISTS FOR (l:Location) ON (l.poi_density, l.commercial_ratio)",
            "CREATE INDEX topic_probability_idx IF NOT EXISTS FOR ()-[r:HAS_TOPIC]-() ON (r.probability)"
        ]
        
        for index in indexes:
            try:
                self.execute_write_query(index)
                self.logger.info(f"Created index: {index.split()[2]}")
            except Exception as e:
                self.logger.warning(f"Index creation failed: {str(e)}")
    
    def clear_database(self) -> None:
        """Clear all data from the database (use with caution)."""
        self.logger.warning("Clearing all data from Neo4j database")
        
        # Delete all relationships first
        self.execute_write_query("MATCH ()-[r]-() DELETE r")
        
        # Delete all nodes
        self.execute_write_query("MATCH (n) DELETE n")
        
        self.logger.info("Database cleared")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        stats_query = """
        CALL apoc.meta.stats() YIELD labels, relTypesCount, propertyKeyCount, nodeCount, relCount
        RETURN labels, relTypesCount, propertyKeyCount, nodeCount, relCount
        """
        
        try:
            result = self.execute_query(stats_query)
            if result:
                return result[0]
            else:
                # Fallback if APOC is not available
                node_count = self.execute_query("MATCH (n) RETURN count(n) as count")[0]['count']
                rel_count = self.execute_query("MATCH ()-[r]-() RETURN count(r) as count")[0]['count']
                
                return {
                    'nodeCount': node_count,
                    'relCount': rel_count,
                    'labels': {},
                    'relTypesCount': {},
                    'propertyKeyCount': 0
                }
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {str(e)}")
            return {}
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            result = self.execute_query("RETURN 'Connection successful' as message")
            return len(result) > 0
        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def backup_user_network(self, user_id: str) -> Dict[str, Any]:
        """Backup a user's network to a dictionary."""
        backup_query = """
        MATCH (u:User {user_id: $user_id})
        OPTIONAL MATCH (u)-[r1:HAS_FAMILIAR_LOCATION]->(l:Location)
        OPTIONAL MATCH (u)-[r2:PREFERS_ACTIVITY]->(a:Activity)
        OPTIONAL MATCH (a1:Activity)-[r3:TRANSITIONS_TO]->(a2:Activity)
        WHERE r3.user_id = $user_id
        RETURN u, collect(DISTINCT {location: l, relationship: r1}) as locations,
               collect(DISTINCT {activity: a, relationship: r2}) as activities,
               collect(DISTINCT {from: a1, to: a2, relationship: r3}) as transitions
        """
        
        result = self.execute_query(backup_query, {'user_id': user_id})
        
        if result:
            return result[0]
        else:
            return {}
    
    def restore_user_network(self, user_id: str, backup_data: Dict[str, Any]) -> None:
        """Restore a user's network from backup data."""
        # This would implement the restoration logic
        # For now, just log the operation
        self.logger.info(f"Restoring network for user {user_id}")
        # Implementation would depend on the specific backup format
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
