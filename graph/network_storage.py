"""Network storage operations for DCMN framework."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import networkx as nx

from graph.neo4j_client import Neo4j<PERSON>lient
from graph.graph_schema import DCMNGraphSchema
from models.contextual_markov_network import ContextualMarkovNetwork


class DCMNNetworkStorage:
    """Storage operations for DCMN networks in Neo4j."""
    
    def __init__(self, neo4j_client: Neo4jClient):
        """Initialize network storage."""
        self.client = neo4j_client
        self.schema = DCMNGraphSchema()
        self.logger = logging.getLogger(__name__)
    
    def initialize_database(self) -> None:
        """Initialize database with constraints and indexes."""
        self.logger.info("Initializing DCMN database schema")
        
        # Create constraints
        constraint_queries = self.schema.get_constraint_queries()
        for query in constraint_queries:
            try:
                self.client.execute_write_query(query)
            except Exception as e:
                self.logger.warning(f"Constraint creation failed: {str(e)}")
        
        # Create indexes
        index_queries = self.schema.get_index_queries()
        for query in index_queries:
            try:
                self.client.execute_write_query(query)
            except Exception as e:
                self.logger.warning(f"Index creation failed: {str(e)}")
        
        self.logger.info("Database schema initialization completed")
    
    def store_user_network(
        self,
        user_id: str,
        cmn_model: ContextualMarkovNetwork,
        user_metadata: Dict[str, Any]
    ) -> None:
        """Store a user's Contextual Markov Network."""
        self.logger.info(f"Storing network for user {user_id}")
        
        # Store user node
        self._store_user_node(user_id, user_metadata)
        
        # Store activity nodes
        self._store_activity_nodes(cmn_model)
        
        # Store activity transitions
        self._store_activity_transitions(user_id, cmn_model)
        
        # Store user preferences
        self._store_user_preferences(user_id, cmn_model)
        
        self.logger.info(f"Network storage completed for user {user_id}")
    
    def _store_user_node(self, user_id: str, metadata: Dict[str, Any]) -> None:
        """Store user node."""
        user_data = {
            'user_id': user_id,
            'age_group': metadata.get('age_group'),
            'gender': metadata.get('gender'),
            'occupation_category': metadata.get('occupation_category'),
            'home_lat': metadata.get('home_latitude'),
            'home_lon': metadata.get('home_longitude'),
            'work_lat': metadata.get('work_latitude'),
            'work_lon': metadata.get('work_longitude'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # Remove None values
        user_data = {k: v for k, v in user_data.items() if v is not None}
        
        query = self.schema.get_merge_node_query('User', 'user_id')
        self.client.execute_write_query(query, user_data)
    
    def _store_activity_nodes(self, cmn_model: ContextualMarkovNetwork) -> None:
        """Store activity nodes."""
        for activity in cmn_model.activity_encoder.classes_:
            activity_data = {
                'name': activity,
                'category': self._get_activity_category(activity),
                'description': f"Activity type: {activity}",
                'created_at': datetime.now().isoformat()
            }
            
            query = self.schema.get_merge_node_query('Activity', 'name')
            self.client.execute_write_query(query, activity_data)
    
    def _store_activity_transitions(
        self,
        user_id: str,
        cmn_model: ContextualMarkovNetwork
    ) -> None:
        """Store activity transition relationships."""
        transition_matrix = cmn_model.transition_matrix
        activities = cmn_model.activity_encoder.classes_
        
        for i, from_activity in enumerate(activities):
            for j, to_activity in enumerate(activities):
                probability = float(transition_matrix[i, j])
                
                if probability > 0.01:  # Only store significant transitions
                    rel_data = {
                        'user_id': user_id,
                        'probability': probability,
                        'count': 1,  # This would be actual count in real implementation
                        'context_features': json.dumps({}),  # Placeholder for context
                        'timestamp': datetime.now().isoformat(),
                        'from_value': from_activity,
                        'to_value': to_activity
                    }
                    
                    query = self.schema.get_merge_relationship_query(
                        'TRANSITIONS_TO', 'Activity', 'Activity', 'name', 'name'
                    )
                    self.client.execute_write_query(query, rel_data)
    
    def _store_user_preferences(
        self,
        user_id: str,
        cmn_model: ContextualMarkovNetwork
    ) -> None:
        """Store user activity preferences."""
        # Calculate activity preferences from transition matrix
        transition_matrix = cmn_model.transition_matrix
        activities = cmn_model.activity_encoder.classes_
        
        # Sum incoming transitions as preference score
        for j, activity in enumerate(activities):
            preference_score = float(transition_matrix[:, j].sum())
            
            if preference_score > 0:
                rel_data = {
                    'preference_score': preference_score,
                    'frequency': preference_score,  # Simplified
                    'last_performed': datetime.now().isoformat(),
                    'from_value': user_id,
                    'to_value': activity
                }
                
                query = self.schema.get_merge_relationship_query(
                    'PREFERS_ACTIVITY', 'User', 'Activity', 'user_id', 'name'
                )
                self.client.execute_write_query(query, rel_data)
    
    def store_location_data(
        self,
        locations_df,
        topic_signatures_df=None,
        topic_regimes_df=None
    ) -> None:
        """Store location nodes and their topic information."""
        self.logger.info("Storing location data")
        
        # Store location nodes
        for _, location in locations_df.iterrows():
            location_data = {
                'circle_id': location['smallest_circle_id'],
                'center_lat': float(location['center_latitude']),
                'center_lon': float(location['center_longitude']),
                'radius_meters': float(location['radius_meters']),
                'poi_density': float(location.get('poi_density', 0)),
                'residential_ratio': float(location.get('residential_ratio', 0)),
                'commercial_ratio': float(location.get('commercial_ratio', 0)),
                'industrial_ratio': float(location.get('industrial_ratio', 0)),
                'recreational_ratio': float(location.get('recreational_ratio', 0)),
                'transportation_ratio': float(location.get('transportation_ratio', 0)),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            query = self.schema.get_merge_node_query('Location', 'circle_id')
            self.client.execute_write_query(query, location_data)
        
        # Store topic signatures if available
        if topic_signatures_df is not None:
            self._store_topic_signatures(topic_signatures_df)
        
        # Store topic regimes if available
        if topic_regimes_df is not None:
            self._store_topic_regimes(topic_regimes_df)
    
    def _store_topic_signatures(self, topic_signatures_df) -> None:
        """Store topic signature relationships."""
        for _, signature in topic_signatures_df.iterrows():
            topic_dist = signature['topic_distribution']
            
            if isinstance(topic_dist, str):
                import ast
                topic_dist = ast.literal_eval(topic_dist)
            
            if isinstance(topic_dist, list):
                for topic_info in topic_dist:
                    if isinstance(topic_info, dict):
                        topic_id = topic_info.get('topic_id')
                        probability = topic_info.get('probability', 0)
                        
                        if topic_id is not None and probability > 0.01:
                            # Store topic node
                            topic_data = {
                                'topic_id': int(topic_id),
                                'name': f"topic_{topic_id}",
                                'top_words': json.dumps(topic_info.get('topic_words', [])),
                                'coherence_score': float(topic_info.get('coherence', 0)),
                                'created_at': datetime.now().isoformat()
                            }
                            
                            query = self.schema.get_merge_node_query('Topic', 'topic_id')
                            self.client.execute_write_query(query, topic_data)
                            
                            # Store HAS_TOPIC relationship
                            rel_data = {
                                'probability': float(probability),
                                'timestamp': signature['timestamp'],
                                'coherence': float(topic_info.get('coherence', 0)),
                                'from_value': signature['smallest_circle_id'],
                                'to_value': int(topic_id)
                            }
                            
                            query = self.schema.get_merge_relationship_query(
                                'HAS_TOPIC', 'Location', 'Topic', 'circle_id', 'topic_id'
                            )
                            self.client.execute_write_query(query, rel_data)
    
    def _store_topic_regimes(self, topic_regimes_df) -> None:
        """Store topic regime relationships."""
        for _, regime in topic_regimes_df.iterrows():
            regime_id = regime['regime_id']
            
            # Store regime node
            regime_data = {
                'regime_id': int(regime_id),
                'name': regime.get('regime_name', f"regime_{regime_id}"),
                'characteristics': json.dumps(regime.get('regime_characteristics', {})),
                'created_at': datetime.now().isoformat()
            }
            
            query = self.schema.get_merge_node_query('Regime', 'regime_id')
            self.client.execute_write_query(query, regime_data)
            
            # Store IN_REGIME relationship
            rel_data = {
                'probability': float(regime['regime_probability']),
                'timestamp': regime['timestamp'],
                'duration': 3600,  # Default 1 hour
                'from_value': regime['smallest_circle_id'],
                'to_value': int(regime_id)
            }
            
            query = self.schema.get_merge_relationship_query(
                'IN_REGIME', 'Location', 'Regime', 'circle_id', 'regime_id'
            )
            self.client.execute_write_query(query, rel_data)
    
    def retrieve_user_network(self, user_id: str) -> Dict[str, Any]:
        """Retrieve a user's network from Neo4j."""
        query = """
        MATCH (u:User {user_id: $user_id})
        OPTIONAL MATCH (u)-[pref:PREFERS_ACTIVITY]->(a:Activity)
        OPTIONAL MATCH (a1:Activity)-[trans:TRANSITIONS_TO {user_id: $user_id}]->(a2:Activity)
        OPTIONAL MATCH (u)-[fam:HAS_FAMILIAR_LOCATION]->(l:Location)
        RETURN u,
               collect(DISTINCT {activity: a, preference: pref}) as preferences,
               collect(DISTINCT {from: a1, to: a2, transition: trans}) as transitions,
               collect(DISTINCT {location: l, familiarity: fam}) as familiar_locations
        """
        
        result = self.client.execute_query(query, {'user_id': user_id})
        
        if result:
            return result[0]
        else:
            return {}
    
    def get_location_context(self, circle_id: str, timestamp: str) -> Dict[str, Any]:
        """Get location context including topics and regimes."""
        query = """
        MATCH (l:Location {circle_id: $circle_id})
        OPTIONAL MATCH (l)-[ht:HAS_TOPIC]->(t:Topic)
        WHERE date(ht.timestamp) = date($timestamp)
        OPTIONAL MATCH (l)-[ir:IN_REGIME]->(r:Regime)
        WHERE date(ir.timestamp) = date($timestamp)
        RETURN l,
               collect(DISTINCT {topic: t, relationship: ht}) as topics,
               collect(DISTINCT {regime: r, relationship: ir}) as regimes
        """
        
        result = self.client.execute_query(query, {
            'circle_id': circle_id,
            'timestamp': timestamp
        })
        
        if result:
            return result[0]
        else:
            return {}
    
    def _get_activity_category(self, activity: str) -> str:
        """Get activity category for classification."""
        category_mapping = {
            'home': 'personal',
            'work': 'professional',
            'shopping': 'commercial',
            'dining': 'commercial',
            'recreation': 'leisure',
            'transportation': 'mobility',
            'social': 'leisure',
            'healthcare': 'services',
            'education': 'services',
            'other': 'miscellaneous'
        }
        
        return category_mapping.get(activity, 'miscellaneous')
