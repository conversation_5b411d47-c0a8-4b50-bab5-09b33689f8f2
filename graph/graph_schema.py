"""Graph schema definitions for DCMN framework."""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json


class DCMNGraphSchema:
    """Schema definitions for DCMN graph database."""
    
    # Node types
    NODE_TYPES = {
        'User': {
            'properties': ['user_id', 'age_group', 'gender', 'occupation_category', 
                          'home_lat', 'home_lon', 'work_lat', 'work_lon', 'created_at', 'updated_at'],
            'required': ['user_id'],
            'indexes': ['user_id']
        },
        'Activity': {
            'properties': ['name', 'category', 'description', 'created_at'],
            'required': ['name'],
            'indexes': ['name']
        },
        'Location': {
            'properties': ['circle_id', 'center_lat', 'center_lon', 'radius_meters',
                          'poi_density', 'residential_ratio', 'commercial_ratio',
                          'industrial_ratio', 'recreational_ratio', 'transportation_ratio',
                          'created_at', 'updated_at'],
            'required': ['circle_id'],
            'indexes': ['circle_id']
        },
        'Topic': {
            'properties': ['topic_id', 'name', 'top_words', 'coherence_score', 'created_at'],
            'required': ['topic_id'],
            'indexes': ['topic_id']
        },
        'Regime': {
            'properties': ['regime_id', 'name', 'characteristics', 'created_at'],
            'required': ['regime_id'],
            'indexes': ['regime_id']
        }
    }
    
    # Relationship types
    RELATIONSHIP_TYPES = {
        'TRANSITIONS_TO': {
            'from': 'Activity',
            'to': 'Activity',
            'properties': ['user_id', 'probability', 'count', 'context_features', 'timestamp'],
            'required': ['user_id', 'probability']
        },
        'HAS_FAMILIAR_LOCATION': {
            'from': 'User',
            'to': 'Location',
            'properties': ['visit_frequency', 'avg_duration', 'last_visit', 'confidence'],
            'required': ['visit_frequency']
        },
        'PREFERS_ACTIVITY': {
            'from': 'User',
            'to': 'Activity',
            'properties': ['preference_score', 'frequency', 'last_performed'],
            'required': ['preference_score']
        },
        'LOCATED_AT': {
            'from': 'Activity',
            'to': 'Location',
            'properties': ['user_id', 'timestamp', 'duration', 'confidence'],
            'required': ['user_id', 'timestamp']
        },
        'HAS_TOPIC': {
            'from': 'Location',
            'to': 'Topic',
            'properties': ['probability', 'timestamp', 'coherence'],
            'required': ['probability']
        },
        'IN_REGIME': {
            'from': 'Location',
            'to': 'Regime',
            'properties': ['probability', 'timestamp', 'duration'],
            'required': ['probability', 'timestamp']
        },
        'REGIME_TRANSITIONS_TO': {
            'from': 'Regime',
            'to': 'Regime',
            'properties': ['location_id', 'probability', 'timestamp'],
            'required': ['location_id', 'probability']
        }
    }
    
    @classmethod
    def get_create_node_query(cls, node_type: str) -> str:
        """Get Cypher query to create a node."""
        if node_type not in cls.NODE_TYPES:
            raise ValueError(f"Unknown node type: {node_type}")
        
        properties = cls.NODE_TYPES[node_type]['properties']
        prop_string = ', '.join([f"{prop}: ${prop}" for prop in properties])
        
        return f"CREATE (n:{node_type} {{{prop_string}}}) RETURN n"
    
    @classmethod
    def get_merge_node_query(cls, node_type: str, unique_property: str) -> str:
        """Get Cypher query to merge a node."""
        if node_type not in cls.NODE_TYPES:
            raise ValueError(f"Unknown node type: {node_type}")
        
        properties = cls.NODE_TYPES[node_type]['properties']
        prop_string = ', '.join([f"n.{prop} = ${prop}" for prop in properties if prop != unique_property])
        
        query = f"MERGE (n:{node_type} {{{unique_property}: ${unique_property}}})"
        if prop_string:
            query += f" SET {prop_string}"
        query += " RETURN n"
        
        return query
    
    @classmethod
    def get_create_relationship_query(
        cls,
        rel_type: str,
        from_node_type: str,
        to_node_type: str,
        from_property: str,
        to_property: str
    ) -> str:
        """Get Cypher query to create a relationship."""
        if rel_type not in cls.RELATIONSHIP_TYPES:
            raise ValueError(f"Unknown relationship type: {rel_type}")
        
        rel_info = cls.RELATIONSHIP_TYPES[rel_type]
        properties = rel_info['properties']
        prop_string = ', '.join([f"{prop}: ${prop}" for prop in properties])
        
        return f"""
        MATCH (from:{from_node_type} {{{from_property}: $from_value}})
        MATCH (to:{to_node_type} {{{to_property}: $to_value}})
        CREATE (from)-[r:{rel_type} {{{prop_string}}}]->(to)
        RETURN r
        """
    
    @classmethod
    def get_merge_relationship_query(
        cls,
        rel_type: str,
        from_node_type: str,
        to_node_type: str,
        from_property: str,
        to_property: str
    ) -> str:
        """Get Cypher query to merge a relationship."""
        if rel_type not in cls.RELATIONSHIP_TYPES:
            raise ValueError(f"Unknown relationship type: {rel_type}")
        
        rel_info = cls.RELATIONSHIP_TYPES[rel_type]
        properties = rel_info['properties']
        prop_string = ', '.join([f"r.{prop} = ${prop}" for prop in properties])
        
        return f"""
        MATCH (from:{from_node_type} {{{from_property}: $from_value}})
        MATCH (to:{to_node_type} {{{to_property}: $to_value}})
        MERGE (from)-[r:{rel_type}]->(to)
        SET {prop_string}
        RETURN r
        """
    
    @classmethod
    def validate_node_data(cls, node_type: str, data: Dict[str, Any]) -> bool:
        """Validate node data against schema."""
        if node_type not in cls.NODE_TYPES:
            return False
        
        schema = cls.NODE_TYPES[node_type]
        required_props = schema['required']
        
        # Check required properties
        for prop in required_props:
            if prop not in data:
                return False
        
        return True
    
    @classmethod
    def validate_relationship_data(cls, rel_type: str, data: Dict[str, Any]) -> bool:
        """Validate relationship data against schema."""
        if rel_type not in cls.RELATIONSHIP_TYPES:
            return False
        
        schema = cls.RELATIONSHIP_TYPES[rel_type]
        required_props = schema['required']
        
        # Check required properties
        for prop in required_props:
            if prop not in data:
                return False
        
        return True
    
    @classmethod
    def get_schema_info(cls) -> Dict[str, Any]:
        """Get complete schema information."""
        return {
            'node_types': cls.NODE_TYPES,
            'relationship_types': cls.RELATIONSHIP_TYPES,
            'version': '1.0.0',
            'created_at': datetime.now().isoformat()
        }
    
    @classmethod
    def get_constraint_queries(cls) -> List[str]:
        """Get all constraint creation queries."""
        constraints = []
        
        for node_type, schema in cls.NODE_TYPES.items():
            for index_prop in schema.get('indexes', []):
                constraint = f"CREATE CONSTRAINT {node_type.lower()}_{index_prop}_unique IF NOT EXISTS FOR (n:{node_type}) REQUIRE n.{index_prop} IS UNIQUE"
                constraints.append(constraint)
        
        return constraints
    
    @classmethod
    def get_index_queries(cls) -> List[str]:
        """Get all index creation queries."""
        indexes = []
        
        # Add common indexes
        common_indexes = [
            "CREATE INDEX user_timestamp_idx IF NOT EXISTS FOR (u:User) ON (u.updated_at)",
            "CREATE INDEX activity_timestamp_idx IF NOT EXISTS FOR ()-[r:TRANSITIONS_TO]-() ON (r.timestamp)",
            "CREATE INDEX location_features_idx IF NOT EXISTS FOR (l:Location) ON (l.poi_density, l.commercial_ratio)",
            "CREATE INDEX topic_probability_idx IF NOT EXISTS FOR ()-[r:HAS_TOPIC]-() ON (r.probability)",
            "CREATE INDEX regime_timestamp_idx IF NOT EXISTS FOR ()-[r:IN_REGIME]-() ON (r.timestamp)"
        ]
        
        indexes.extend(common_indexes)
        return indexes
