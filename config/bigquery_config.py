"""BigQuery configuration for DCMN framework."""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class BigQueryConfig:
    """Configuration for BigQuery connections and datasets."""
    
    project_id: str
    dataset_id: str
    credentials_path: Optional[str] = None
    location: str = "US"
    
    # Table names
    activity_sequences_table: str = "activity_sequences"
    location_features_table: str = "location_features"
    user_metadata_table: str = "user_metadata"
    osm_data_table: str = "osm_data"
    textual_data_table: str = "textual_data"
    
    @classmethod
    def from_env(cls) -> "BigQueryConfig":
        """Create configuration from environment variables."""
        return cls(
            project_id=os.getenv("BIGQUERY_PROJECT_ID", "dcmn-project"),
            dataset_id=os.getenv("BIGQUERY_DATASET_ID", "dcmn_dataset"),
            credentials_path=os.getenv("GOOGLE_APPLICATION_CREDENTIALS"),
            location=os.getenv("BIGQUERY_LOCATION", "US"),
        )
    
    def get_table_id(self, table_name: str) -> str:
        """Get fully qualified table ID."""
        return f"{self.project_id}.{self.dataset_id}.{table_name}"
    
    def validate(self) -> bool:
        """Validate configuration."""
        if not self.project_id:
            raise ValueError("BigQuery project_id is required")
        if not self.dataset_id:
            raise ValueError("BigQuery dataset_id is required")
        return True
