"""Model configuration for DCMN framework."""

from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class LDAConfig:
    """Configuration for LDA topic modeling."""
    
    num_topics: int = 10
    alpha: str = "auto"
    beta: str = "auto"
    iterations: int = 1000
    passes: int = 10
    random_state: int = 42
    minimum_probability: float = 0.01
    per_word_topics: bool = True


@dataclass
class HMMConfig:
    """Configuration for Hidden Markov Models."""
    
    n_components: int = 5
    covariance_type: str = "full"
    n_iter: int = 100
    tol: float = 1e-2
    random_state: int = 42
    algorithm: str = "viterbi"


@dataclass
class CMNConfig:
    """Configuration for Contextual Markov Networks."""
    
    activity_types: List[str] = None
    max_familiar_locations: int = 25
    spatial_threshold_meters: float = 100.0
    temporal_window_hours: int = 24
    transition_smoothing: float = 0.1
    emission_smoothing: float = 0.1
    
    def __post_init__(self):
        if self.activity_types is None:
            self.activity_types = [
                "home", "work", "shopping", "dining", "recreation",
                "transportation", "social", "healthcare", "education", "other"
            ]


@dataclass
class OptimizationConfig:
    """Configuration for optimization algorithms."""
    
    solver: str = "PULP_CBC_CMD"
    time_limit_seconds: int = 300
    gap_tolerance: float = 0.01
    threads: int = 4


@dataclass
class ModelConfig:
    """Main model configuration container."""
    
    lda: LDAConfig = None
    hmm: HMMConfig = None
    cmn: CMNConfig = None
    optimization: OptimizationConfig = None
    
    def __post_init__(self):
        if self.lda is None:
            self.lda = LDAConfig()
        if self.hmm is None:
            self.hmm = HMMConfig()
        if self.cmn is None:
            self.cmn = CMNConfig()
        if self.optimization is None:
            self.optimization = OptimizationConfig()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "ModelConfig":
        """Create configuration from dictionary."""
        return cls(
            lda=LDAConfig(**config_dict.get("lda", {})),
            hmm=HMMConfig(**config_dict.get("hmm", {})),
            cmn=CMNConfig(**config_dict.get("cmn", {})),
            optimization=OptimizationConfig(**config_dict.get("optimization", {})),
        )
