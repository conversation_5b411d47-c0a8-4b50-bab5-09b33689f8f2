"""Neo4j configuration for DCMN framework."""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class Neo4jConfig:
    """Configuration for Neo4j graph database connections."""
    
    uri: str
    username: str
    password: str
    database: str = "neo4j"
    max_connection_lifetime: int = 3600
    max_connection_pool_size: int = 50
    connection_acquisition_timeout: int = 60
    
    @classmethod
    def from_env(cls) -> "Neo4jConfig":
        """Create configuration from environment variables."""
        return cls(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            username=os.getenv("NEO4J_USERNAME", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE", "neo4j"),
        )
    
    def validate(self) -> bool:
        """Validate configuration."""
        if not self.uri:
            raise ValueError("Neo4j URI is required")
        if not self.username:
            raise ValueError("Neo4j username is required")
        if not self.password:
            raise ValueError("Neo4j password is required")
        return True
    
    def get_connection_config(self) -> dict:
        """Get connection configuration dictionary."""
        return {
            "max_connection_lifetime": self.max_connection_lifetime,
            "max_connection_pool_size": self.max_connection_pool_size,
            "connection_acquisition_timeout": self.connection_acquisition_timeout,
        }
