# Dynamic Contextual Markov Networks for Individual Activity Inference

This repository implements a complete framework for Dynamic Contextual Markov Networks (DCMN) for individual activity inference, based on the research proposal that integrates OSM-derived spatial units, probabilistic anchor points, optimized familiar location sets, and LDA-HMM derived location-topic regimes.

## Overview

The framework addresses the challenge of inferring individual activity types without direct "user visit purpose" data by:

- Defining fine-grained spatial units ("smallest circles") from OpenStreetMap (OSM) road networks
- Modeling key anchor points (home, office) and other significant locations probabilistically
- Explicitly identifying and dynamically managing an individual's set of ~25 probabilistic "familiar locations"
- **Characterizing locations with dynamic "Location-Topic Signatures" derived using Latent Dirichlet Allocation (LDA) on associated textual data**
- **Modeling the temporal evolution of these Location-Topic Signatures using Hidden Markov Models (HMMs) to identify distinct "Location-Topic Regimes"**
- Utilizing a Contextual Markov Network (CMN) where the context is dynamically enriched by these familiar locations, the current Location-Topic Regime of the visited place, OSM characteristics, and general activity likelihoods
- Employing optimization algorithms (e.g., Integer Programming, pruning) to manage the dynamic evolution of the familiar location set

## Key Features

### 1. BigQuery Data Preparation
- Sample datasets representing individual activity data
- Temporal activity sequences, contextual features, and user metadata
- SQL queries for data preprocessing and feature extraction
- Scalable data pipeline for large-scale activity inference

### 2. Python Model Training Framework
- **LDA Topic Modeling**: Extract location-topic signatures from textual data
- **Hidden Markov Models**: Model temporal evolution of location-topic regimes
- **Contextual Markov Networks**: Core activity inference engine
- **Familiar Location Management**: Dynamic optimization of user-specific location sets
- Comprehensive error handling, logging, and configuration management

### 3. Neo4j Graph Database Integration
- Graph schema for storing individual Contextual Markov Networks
- User-specific network structures and relationships
- Efficient querying and retrieval for real-time inference
- Dynamic network updates as new data becomes available

### 4. End-to-End Pipeline
- Complete workflow: BigQuery → Python Training → Neo4j Storage
- Modular and extensible architecture
- Real-time activity inference capabilities
- Comprehensive testing and validation

## Installation

### Prerequisites
- Python 3.8+
- Google Cloud Platform account with BigQuery access
- Neo4j database (local or cloud)
- Required Python packages (see requirements.txt)

### Setup Instructions

1. **Clone the repository:**
```bash
git clone https://github.com/dcmn-research/dcmn-framework.git
cd dcmn-framework
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Download required NLTK data:**
```bash
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet'); nltk.download('averaged_perceptron_tagger')"
```

4. **Install spaCy model:**
```bash
python -m spacy download en_core_web_sm
```

5. **Set up environment variables:**
```bash
export BIGQUERY_PROJECT_ID="your-project-id"
export BIGQUERY_DATASET_ID="dcmn_dataset"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USERNAME="neo4j"
export NEO4J_PASSWORD="your-password"
```

6. **Initialize BigQuery dataset:**
```bash
# Create dataset and tables using the provided SQL schemas
bq mk --dataset your-project-id:dcmn_dataset
```

7. **Setup Neo4j database:**
```bash
# Start Neo4j and create constraints/indexes
# The framework will automatically create the schema on first run
```

## Quick Start

### 1. Prepare Sample Data

First, create the BigQuery tables and insert sample data:

```bash
# Execute the schema creation
bq query --use_legacy_sql=false < data/bigquery/schemas.sql

# Insert sample data
bq query --use_legacy_sql=false < data/bigquery/sample_data.sql
```

### 2. Run Training Pipeline

```python
from pipeline.training_pipeline import DCMNTrainingPipeline

# Create configuration
config_path = "config.yaml"  # See examples/config.yaml

# Initialize and run pipeline
pipeline = DCMNTrainingPipeline(config_path)
result = pipeline.run_complete_pipeline()

print(f"Training completed: {result['status']}")
```

### 3. Run End-to-End Example

```bash
python examples/end_to_end_example.py
```

## Architecture Overview

The DCMN framework consists of four main components:

### 1. Data Layer (BigQuery)
- **Activity Sequences**: Temporal GPS traces with activity labels
- **Location Features**: OSM-derived spatial characteristics
- **User Metadata**: Demographics and anchor points (home/work)
- **Textual Data**: Location-associated text for topic modeling
- **Topic Signatures**: LDA-derived location characterizations
- **Topic Regimes**: HMM-detected temporal patterns

### 2. Model Layer (Python)
- **LDA Topic Model**: Extracts location-topic signatures from textual data
- **HMM Regime Model**: Detects temporal evolution of location topics
- **Contextual Markov Network**: Core activity inference engine
- **Familiar Location Optimizer**: Manages user-specific location sets

### 3. Graph Layer (Neo4j)
- **User Networks**: Individual Contextual Markov Networks
- **Location Nodes**: Spatial units with topic and regime information
- **Activity Transitions**: Context-dependent transition probabilities
- **Temporal Relationships**: Time-aware network evolution

### 4. Pipeline Layer
- **Training Pipeline**: End-to-end model training workflow
- **Inference Pipeline**: Real-time activity prediction
- **Optimization Pipeline**: Dynamic familiar location management

## Detailed Usage

### Training Custom Models

```python
from config.model_config import ModelConfig, LDAConfig, HMMConfig, CMNConfig

# Configure models
lda_config = LDAConfig(num_topics=10, iterations=1000)
hmm_config = HMMConfig(n_components=5, n_iter=100)
cmn_config = CMNConfig(max_familiar_locations=25)

model_config = ModelConfig(lda=lda_config, hmm=hmm_config, cmn=cmn_config)

# Train models with custom configuration
# ... (see training_pipeline.py for details)
```

### Real-time Activity Inference

```python
from models.contextual_markov_network import ContextualMarkovNetwork

# Load trained model
cmn = ContextualMarkovNetwork.load_model("path/to/model.pkl")

# Prepare context features
context = {
    'temporal': {'hour_of_day': 0.5, 'is_weekend': 0.0},
    'spatial': {'poi_density': 0.3, 'commercial_ratio': 0.6},
    'familiarity': {'is_familiar_location': 1.0},
    'topic': {'topic_1_prob': 0.7}  # Dining topic
}

# Predict activity
predictions = cmn.predict_activity(context, previous_activity='work')
print(f"Predicted activity: {predictions[0]['activity']}")
```

### Querying Neo4j Networks

```python
from graph.neo4j_client import Neo4jClient
from config.neo4j_config import Neo4jConfig

# Connect to Neo4j
config = Neo4jConfig.from_env()
client = Neo4jClient(config)

# Query user's activity patterns
query = """
MATCH (u:User {user_id: $user_id})-[r:PREFERS_ACTIVITY]->(a:Activity)
RETURN a.name, r.preference_score
ORDER BY r.preference_score DESC
"""

results = client.execute_query(query, {'user_id': 'user_001'})
for result in results:
    print(f"Activity: {result['a.name']}, Score: {result['r.preference_score']}")
```

## Testing

Run the test suite to validate the framework:

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_models.py

# Run with coverage
python -m pytest tests/ --cov=. --cov-report=html
```

## Configuration

The framework uses YAML configuration files. Key configuration sections:

### Model Configuration
```yaml
models:
  lda:
    num_topics: 10
    iterations: 1000
  hmm:
    n_components: 5
    n_iter: 100
  cmn:
    max_familiar_locations: 25
    spatial_threshold_meters: 100.0
```

### Data Configuration
```yaml
data:
  start_date: "2024-01-01"
  end_date: "2024-01-31"
  min_confidence: 0.7
```

## API Reference

### Core Classes

#### `LocationLDAModel`
Extracts location-topic signatures using Latent Dirichlet Allocation.

**Methods:**
- `train(processed_docs)`: Train LDA model on processed documents
- `get_location_topic_signature(location_text)`: Get topic signature for location
- `save_model(filepath)`: Save trained model
- `load_model(filepath)`: Load trained model

#### `LocationTopicRegimeHMM`
Detects temporal evolution of location-topic regimes using Hidden Markov Models.

**Methods:**
- `prepare_sequences(topic_signatures_df)`: Prepare sequences for training
- `train(sequences)`: Train HMM model
- `predict_regime(topic_signature, timestamp)`: Predict regime for observation

#### `ContextualMarkovNetwork`
Core activity inference engine using contextual Markov networks.

**Methods:**
- `prepare_training_data(...)`: Prepare training data from multiple sources
- `train(training_data)`: Train CMN model
- `predict_activity(context, previous_activity)`: Predict activity given context

#### `DCMNNetworkStorage`
Manages storage and retrieval of networks in Neo4j.

**Methods:**
- `store_user_network(user_id, cmn_model, metadata)`: Store user's network
- `retrieve_user_network(user_id)`: Retrieve user's network
- `get_location_context(circle_id, timestamp)`: Get location context

## Performance Considerations

### Scalability
- **BigQuery**: Handles large-scale data with partitioning and clustering
- **Neo4j**: Optimized for graph queries with proper indexing
- **Python Models**: Memory-efficient implementations with batch processing

### Optimization Tips
1. **Data Preprocessing**: Use appropriate batch sizes for large datasets
2. **Model Training**: Adjust iteration counts based on data size and convergence
3. **Neo4j Queries**: Use EXPLAIN to optimize Cypher queries
4. **Memory Management**: Monitor memory usage during training

## Troubleshooting

### Common Issues

1. **BigQuery Authentication**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/credentials.json"
   gcloud auth application-default login
   ```

2. **Neo4j Connection**
   ```bash
   # Check Neo4j status
   neo4j status

   # Start Neo4j
   neo4j start
   ```

3. **Memory Issues**
   - Reduce batch sizes in configuration
   - Use data sampling for initial testing
   - Monitor memory usage with `htop` or similar tools

4. **Model Convergence**
   - Increase iteration counts for LDA/HMM
   - Check data quality and preprocessing
   - Adjust model hyperparameters

### Logging

The framework provides comprehensive logging:

```python
from utils.logging_config import setup_logging

# Setup logging
setup_logging(level="DEBUG", log_file="dcmn.log")

# Logs are written to both console and file
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt
pip install -e .

# Install pre-commit hooks
pre-commit install

# Run tests
python -m pytest tests/
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Citation

If you use this framework in your research, please cite:

```bibtex
@software{dcmn_framework,
  title={Dynamic Contextual Markov Networks for Individual Activity Inference},
  author={DCMN Research Team},
  year={2024},
  url={https://github.com/dcmn-research/dcmn-framework}
}
```

## Acknowledgments

- Inspired by the research on Latent Dirichlet Allocation and Hidden Markov Models by Cao Pinna et al.
- Built on top of excellent open-source libraries: scikit-learn, gensim, NetworkX, Neo4j, and Google Cloud BigQuery
- Special thanks to the OpenStreetMap community for providing rich geospatial data

## Research Background

**Original Research Proposal: Dynamic Contextual Markov Networks for Individual Activity Inference using OSM-Derived Smallest Circles, Probabilistic Anchor Points, Optimized Familiar Location Sets, and LDA-HMM Derived Location-Topic Regimes**

**2.1. OSM-Derived "Smallest Circles" as Adaptive Spatial Units**
(Same as before)

**2.2. Probabilistic Anchor Points: Home & Office**
(Same as before)

**2.3. Dynamic Probabilistic Set of ~25 Familiar Locations**
(Same as before – identification, representation, and dynamic evolution managed by IP/pruning)

**2.4. Deriving Dynamic Location-Topic Signatures and Regimes (NEW SECTION - adapting Cao Pinna et al.)**
This section addresses the lack of direct visit purpose by characterizing the *context of locations* thematically and temporally.

*   **2.4.1. Data Collection for Location Characterization:**
    *   For each "smallest circle" (or potentially slightly larger, semantically meaningful aggregates of circles, e.g., "activity zones" or "neighborhood blocks" to ensure sufficient textual data):
        *   Collect associated textual data over time. This could include:
            *   Detailed OSM tags: names of businesses, amenity types, descriptions.
            *   Aggregated and anonymized public social media posts geotagged to the location/zone.
            *   Local news snippets or event listings pertaining to the area.
            *   Business reviews or descriptions.
    *   This text is pre-processed (stopwords, stemming, etc.). Each "document" for LDA could be the aggregated text for a specific location/zone within a defined time window (e.g., daily, weekly).

*   **2.4.2. Latent Dirichlet Allocation (LDA) for Topic Extraction:**
    *   Apply LDA to the corpus of location-based documents (from 2.4.1) to identify a set of latent "location topics" (e.g., "dining-out," "shopping-retail," "outdoor-recreation," "business-services," "evening-entertainment," "transit-hub").
    *   Each location/zone at a given time `t` will then be represented by a probability distribution over these `K` topics (its "Location-Topic Signature" for that time).

*   **2.4.3. Hidden Markov Models (HMMs) for Location-Topic Regime Detection:**
    *   **Concept:** Similar to Cao Pinna et al., we assume that the *prevalence of these location topics* within a given location/zone may change over time, reflecting shifts in the area's function, special events, or public interest.
    *   **HMM Structure:**
        *   **States:** The hidden states of the HMM will represent different "Location-Topic Regimes" (e.g., "Normal Business Hours," "Weekend Shopping Peak," "Special Event Active," "Quiet Residential Evening"). Each regime `k` is characterized by a distinct probability distribution `m(k)` over the `K` location topics (i.e., topic prevalence for that regime).
        *   **Observations:** The sequence of Location-Topic Signatures (topic distributions from LDA) for a specific location/zone over discrete time intervals.
        *   **Transitions:** The HMM will model the probability of transitioning from one Location-Topic Regime to another, or staying in the current regime. This allows for detecting changepoints in how a location is thematically characterized.
    *   **Output:** For any given location/zone at time `t`, the HMM will provide the probability of it being in each Location-Topic Regime. The most probable regime can be assigned.

**2.5. Probabilistic Activity Signatures for Smallest Circles (REVISED)**
*   This section is now significantly enhanced. The "Probabilistic Activity Signature" for a smallest circle is no longer just based on static OSM categories or general behavior.
*   It's now primarily defined by its **current Location-Topic Signature (from LDA) and its current HMM-inferred Location-Topic Regime.**
*   For example, if a circle is in a "Dining-Out Topic Heavy" regime, the probability of "eating" activity for an individual there increases.

**2.6. Contextual Markov Network (CMN) for Activity Inference**
(States and Observations remain similar)
*   **Enhanced Dynamic Context Features (Input to the CMN transition/emission probabilities):**
    1.  **Current Smallest Circle Characteristics:**
        *   OSM-derived features (as before).
        *   **NEW:** The current **Location-Topic Signature** (topic distribution from LDA for this circle at this time).
        *   **NEW:** The current **most probable Location-Topic Regime** (from HMM for this circle at this time).
    2.  **Familiarity Context:** (Same as before)
        *   Is the current smallest circle part of the individual’s current set of ~25 familiar locations?
        *   If yes, individual-specific historical activity patterns for this familiar location.
    3.  **Anchor Point Context:** (Same as before)
    4.  **Temporal Context:** (Same as before)
    5.  **Recent Activity Context:** (Same as before)
    6.  **Mobility Context:** (Same as before)

**2.7. Optimization for Dynamic Familiar Sets & CMN Robustness**
(The role of IP/pruning remains the same: managing the *individual's* dynamic set of ~25 familiar locations. This is distinct from, but complementary to, the LDA-HMM characterization of the general environment.)

*   **Managing the Dynamic Set of ~25 Familiar Locations:** (IP/Pruning as described before)
*   **Enhancing CMN Completeness for New/Rare Places:**
    *   When an individual visits a *new* "smallest circle" (not in their familiar set):
        *   The CMN will heavily rely on the context derived from the **LDA Location-Topic Signature** and the **HMM Location-Topic Regime** of that new place (from 2.4), alongside general OSM features and temporal context.
        *   If a place has a "rare character" (e.g., a pop-up market), its textual data should reflect this, allowing LDA to pick it up as a niche topic or a unique topic mix. The HMM might identify this as a specific, possibly short-lived, Location-Topic Regime.
    *   The optimization algorithms (IP/pruning) for the user's familiar set ensure that the *individual-specific context* is always focused on their currently most relevant ~25 places. For places outside this set, the model leans more on the general, LDA-HMM derived characterization of the location.

**3. Workflow**
1.  **Offline/Periodic Model Building (Location Characterization):**
    *   Collect textual data associated with smallest circles/zones over time.
    *   Train LDA models to define location topics.
    *   Train HMMs for each location/zone (or types of zones) to identify Location-Topic Regimes and learn transition dynamics.
2.  **Online/Real-time Activity Inference (Individual):**
    *   Individual location data (GPS) is collected.
    *   Each point is mapped to an OSM "smallest circle."
    *   Probabilistic home/office are (re)calculated.
    *   The set of ~25 familiar locations for the individual is updated using IP/pruning.
    *   For the current "smallest circle" and individual:
        *   Retrieve its static OSM features.
        *   Retrieve/infer its current Location-Topic Signature and most probable Location-Topic Regime (using the pre-trained LDA/HMM models).
        *   Determine its status relative to the individual's ~25 familiar locations and home/office.
        *   Gather temporal and recent activity context.
    *   These context features are fed into the CMN.
    *   The CMN infers the most likely sequence of activities.
3.  The system learns and adapts over time (both the location characterization models and the individual's familiar set).

**4. Expected Contributions & Novelty**
*   (As before, plus...)
*   A novel method to infer rich, dynamic, and thematic context for locations ("Location-Topic Regimes") using LDA and HMMs on auxiliary textual data, specifically to compensate for the lack of direct user-provided visit purpose.
*   Enhanced activity inference by incorporating these dynamic Location-Topic Regimes as a key contextual input into the CMN.
*   The use of optimization (IP/pruning) to actively manage the individual's evolving set of familiar locations remains a distinct contribution, now complemented by the LDA-HMM environmental characterization for completeness.

**5. Confirmation of Novelty (Survey)**
(Keywords as before, now add):
*   "LDA HMM location context," "topic modeling activity recognition," "spatiotemporal topic models human mobility."
*   "Dynamic location characterization activity inference."

The key novelty here is the sophisticated way you're proposing to generate a dynamic, thematic context for locations using LDA-HMM (inspired by Cao Pinna et al. but applied differently – to characterize places rather than just public perception on a single theme) and then *feeding this evolving place-context* into your already advanced CMN for individual activity inference, alongside the personalized familiar location set. This directly tackles the "no user visit purpose data" issue by making the *places themselves tell a story* about what might be happening there.