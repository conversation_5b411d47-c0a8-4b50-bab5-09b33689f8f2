"""Training pipeline for DCMN framework."""

import logging
import os
import yaml
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd

from config.bigquery_config import BigQueryConfig
from config.neo4j_config import Neo4jConfig
from config.model_config import ModelConfig
from data.loaders.bigquery_loader import BigQueryLoader
from data.preprocessing.text_processor import TextProcessor
from models.lda_model import LocationLDAModel
from models.hmm_model import LocationTopicRegimeHMM
from models.contextual_markov_network import ContextualMarkovNetwork
from graph.neo4j_client import Neo4jClient
from graph.network_storage import DCMNNetworkStorage
from utils.logging_config import setup_logging


class DCMNTrainingPipeline:
    """Complete training pipeline for DCMN framework."""
    
    def __init__(self, config_path: str):
        """Initialize training pipeline."""
        self.config_path = config_path
        self.config = self._load_config()
        
        # Setup logging
        setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.bigquery_config = BigQueryConfig.from_env()
        self.neo4j_config = Neo4jConfig.from_env()
        self.model_config = ModelConfig.from_dict(self.config.get('models', {}))
        
        # Initialize clients
        self.bq_loader = BigQueryLoader(self.bigquery_config)
        self.neo4j_client = Neo4jClient(self.neo4j_config)
        self.network_storage = DCMNNetworkStorage(self.neo4j_client)
        
        # Initialize models
        self.text_processor = TextProcessor()
        self.lda_model = LocationLDAModel(self.model_config.lda)
        self.hmm_model = LocationTopicRegimeHMM(self.model_config.hmm)
        self.cmn_model = ContextualMarkovNetwork(self.model_config.cmn)
        
        # Training metadata
        self.training_metadata = {
            'pipeline_version': '1.0.0',
            'start_time': datetime.now().isoformat(),
            'config': self.config
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Return default configuration
            return {
                'data': {
                    'start_date': '2024-01-01',
                    'end_date': '2024-01-31',
                    'min_confidence': 0.7
                },
                'models': {},
                'output': {
                    'model_dir': './models',
                    'save_models': True
                }
            }
    
    def run_complete_pipeline(self) -> Dict[str, Any]:
        """Run the complete training pipeline."""
        self.logger.info("Starting DCMN training pipeline")
        
        try:
            # Step 1: Data preparation
            self.logger.info("Step 1: Data preparation")
            data = self._prepare_data()
            
            # Step 2: LDA topic modeling
            self.logger.info("Step 2: LDA topic modeling")
            topic_signatures = self._train_lda_model(data)
            
            # Step 3: HMM regime detection
            self.logger.info("Step 3: HMM regime detection")
            topic_regimes = self._train_hmm_model(topic_signatures)
            
            # Step 4: CMN training
            self.logger.info("Step 4: CMN training")
            self._train_cmn_model(data, topic_signatures, topic_regimes)
            
            # Step 5: Neo4j storage
            self.logger.info("Step 5: Neo4j storage")
            self._store_networks(data, topic_signatures, topic_regimes)
            
            # Step 6: Save models
            if self.config.get('output', {}).get('save_models', True):
                self.logger.info("Step 6: Saving models")
                self._save_models()
            
            # Update metadata
            self.training_metadata.update({
                'end_time': datetime.now().isoformat(),
                'status': 'completed',
                'data_stats': self._get_data_stats(data),
                'model_stats': self._get_model_stats()
            })
            
            self.logger.info("DCMN training pipeline completed successfully")
            return self.training_metadata
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {str(e)}")
            self.training_metadata.update({
                'end_time': datetime.now().isoformat(),
                'status': 'failed',
                'error': str(e)
            })
            raise
        
        finally:
            self._cleanup()
    
    def _prepare_data(self) -> Dict[str, pd.DataFrame]:
        """Prepare training data from BigQuery."""
        data_config = self.config.get('data', {})
        start_date = datetime.fromisoformat(data_config.get('start_date', '2024-01-01'))
        end_date = datetime.fromisoformat(data_config.get('end_date', '2024-01-31'))
        min_confidence = data_config.get('min_confidence', 0.7)
        
        # Load activity sequences
        activity_query = f"""
        SELECT * FROM `{self.bigquery_config.get_table_id('activity_sequences')}`
        WHERE timestamp BETWEEN '{start_date.isoformat()}' AND '{end_date.isoformat()}'
        AND confidence_score >= {min_confidence}
        """
        activity_sequences = self.bq_loader.execute_query(activity_query)
        
        # Load location features
        unique_circles = activity_sequences['smallest_circle_id'].unique().tolist()
        location_features = self.bq_loader.load_location_features(unique_circles)
        
        # Load user metadata
        unique_users = activity_sequences['user_id'].unique().tolist()
        user_query = f"""
        SELECT * FROM `{self.bigquery_config.get_table_id('user_metadata')}`
        WHERE user_id IN ('{"', '".join(unique_users)}')
        """
        user_metadata = self.bq_loader.execute_query(user_query)
        
        # Load textual data for LDA
        textual_data = self.bq_loader.load_textual_data_for_lda(start_date, end_date)
        
        return {
            'activity_sequences': activity_sequences,
            'location_features': location_features,
            'user_metadata': user_metadata,
            'textual_data': textual_data
        }
    
    def _train_lda_model(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Train LDA model for location topic signatures."""
        textual_data = data['textual_data']
        
        if textual_data.empty:
            self.logger.warning("No textual data available for LDA training")
            return pd.DataFrame()
        
        # Create location documents
        location_docs = self.text_processor.create_location_documents(
            textual_data,
            text_column='text_content',
            location_column='smallest_circle_id'
        )
        
        # Process documents
        processed_docs = []
        doc_metadata = []
        
        for doc_id, tokens in location_docs.items():
            processed_docs.append(tokens)
            doc_metadata.append({
                'document_id': doc_id,
                'smallest_circle_id': doc_id.split('_')[0] if '_' in doc_id else doc_id
            })
        
        # Train LDA model
        self.lda_model.train(processed_docs)
        
        # Generate topic signatures for all location-time combinations
        topic_signatures = []
        
        for i, (doc_id, tokens) in enumerate(location_docs.items()):
            signature = self.lda_model.get_location_topic_signature(tokens)
            
            topic_signatures.append({
                'smallest_circle_id': doc_metadata[i]['smallest_circle_id'],
                'timestamp': datetime.now().isoformat(),
                'topic_distribution': signature,
                'dominant_topic_id': signature.get('dominant_topic', {}).get('topic_id'),
                'topic_coherence': self.lda_model.training_metadata.get('coherence_score', 0.0)
            })
        
        # Store topic signatures in BigQuery
        if topic_signatures:
            self.bq_loader.insert_topic_signatures(topic_signatures)
        
        return pd.DataFrame(topic_signatures)
    
    def _train_hmm_model(self, topic_signatures_df: pd.DataFrame) -> pd.DataFrame:
        """Train HMM model for topic regime detection."""
        if topic_signatures_df.empty:
            self.logger.warning("No topic signatures available for HMM training")
            return pd.DataFrame()
        
        # Prepare sequences for HMM
        sequences = self.hmm_model.prepare_sequences(topic_signatures_df)
        
        if not sequences:
            self.logger.warning("No valid sequences for HMM training")
            return pd.DataFrame()
        
        # Train HMM model
        self.hmm_model.train(sequences)
        
        # Generate regime predictions
        topic_regimes = []
        
        for location_id, seq_data in sequences.items():
            observations = seq_data['observations']
            timestamps = seq_data['timestamps']
            
            for i, (obs, timestamp) in enumerate(zip(observations, timestamps)):
                # Extract topic signature (first part of observation)
                topic_signature = obs[:len(obs) - 7]  # Remove temporal features
                
                regime_prediction = self.hmm_model.predict_regime(topic_signature, timestamp)
                
                topic_regimes.append({
                    'smallest_circle_id': location_id,
                    'timestamp': timestamp,
                    'regime_id': regime_prediction['regime_id'],
                    'regime_probability': regime_prediction['regime_probability'],
                    'regime_name': regime_prediction['regime_name'],
                    'regime_characteristics': {},
                    'transition_probability': 0.0  # Would be calculated from sequence
                })
        
        # Store topic regimes in BigQuery
        if topic_regimes:
            self.bq_loader.insert_topic_regimes(topic_regimes)
        
        return pd.DataFrame(topic_regimes)
    
    def _train_cmn_model(
        self,
        data: Dict[str, pd.DataFrame],
        topic_signatures_df: pd.DataFrame,
        topic_regimes_df: pd.DataFrame
    ) -> None:
        """Train Contextual Markov Network."""
        # Prepare training data
        training_data = self.cmn_model.prepare_training_data(
            data['activity_sequences'],
            data['location_features'],
            data['user_metadata'],
            topic_signatures_df if not topic_signatures_df.empty else None,
            topic_regimes_df if not topic_regimes_df.empty else None
        )
        
        # Train CMN
        self.cmn_model.train(training_data)
    
    def _store_networks(
        self,
        data: Dict[str, pd.DataFrame],
        topic_signatures_df: pd.DataFrame,
        topic_regimes_df: pd.DataFrame
    ) -> None:
        """Store networks in Neo4j."""
        # Initialize database schema
        self.network_storage.initialize_database()
        
        # Store location data
        self.network_storage.store_location_data(
            data['location_features'],
            topic_signatures_df if not topic_signatures_df.empty else None,
            topic_regimes_df if not topic_regimes_df.empty else None
        )
        
        # Store user networks
        for _, user in data['user_metadata'].iterrows():
            user_id = user['user_id']
            user_metadata = user.to_dict()
            
            self.network_storage.store_user_network(
                user_id,
                self.cmn_model,
                user_metadata
            )
    
    def _save_models(self) -> None:
        """Save trained models to disk."""
        model_dir = self.config.get('output', {}).get('model_dir', './models')
        os.makedirs(model_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save LDA model
        lda_path = os.path.join(model_dir, f'lda_model_{timestamp}.pkl')
        self.lda_model.save_model(lda_path)
        
        # Save HMM model
        hmm_path = os.path.join(model_dir, f'hmm_model_{timestamp}.pkl')
        self.hmm_model.save_model(hmm_path)
        
        # Save CMN model
        cmn_path = os.path.join(model_dir, f'cmn_model_{timestamp}.pkl')
        self.cmn_model.save_model(cmn_path)
        
        self.logger.info(f"Models saved to {model_dir}")
    
    def _get_data_stats(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Get data statistics."""
        return {
            'activity_sequences_count': len(data['activity_sequences']),
            'unique_users': data['activity_sequences']['user_id'].nunique(),
            'unique_locations': data['activity_sequences']['smallest_circle_id'].nunique(),
            'unique_activities': data['activity_sequences']['activity_type'].nunique(),
            'textual_documents': len(data['textual_data'])
        }
    
    def _get_model_stats(self) -> Dict[str, Any]:
        """Get model statistics."""
        return {
            'lda_model': self.lda_model.get_model_info(),
            'hmm_model': self.hmm_model.get_model_info(),
            'cmn_model': self.cmn_model.get_model_info()
        }
    
    def _cleanup(self) -> None:
        """Cleanup resources."""
        try:
            self.bq_loader.close()
            self.neo4j_client.close()
        except Exception as e:
            self.logger.warning(f"Cleanup warning: {str(e)}")


def main():
    """Main entry point for training pipeline."""
    import argparse
    
    parser = argparse.ArgumentParser(description='DCMN Training Pipeline')
    parser.add_argument('--config', default='config.yaml', help='Configuration file path')
    args = parser.parse_args()
    
    pipeline = DCMNTrainingPipeline(args.config)
    result = pipeline.run_complete_pipeline()
    
    print("Training completed successfully!")
    print(f"Results: {result}")


if __name__ == '__main__':
    main()
