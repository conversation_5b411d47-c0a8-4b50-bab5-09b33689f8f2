"""LDA topic modeling for location characterization."""

import logging
import pickle
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime
import gensim
from gensim import corpora
from gensim.models import LdaModel, CoherenceModel
from gensim.models.callbacks import CallbackAny2Vec

from config.model_config import LDAConfig


class LDATrainingCallback(CallbackAny2Vec):
    """Callback to track LDA training progress."""
    
    def __init__(self):
        self.epoch = 0
        self.logger = logging.getLogger(__name__)
    
    def on_epoch_end(self, model):
        self.epoch += 1
        if self.epoch % 10 == 0:
            self.logger.info(f"LDA training epoch {self.epoch} completed")


class LocationLDAModel:
    """LDA model for extracting location topic signatures."""
    
    def __init__(self, config: LDAConfig):
        """Initialize LDA model."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model components
        self.dictionary = None
        self.corpus = None
        self.lda_model = None
        self.topic_names = None
        
        # Training metadata
        self.training_metadata = {}
    
    def prepare_corpus(self, processed_docs: List[List[str]]) -> None:
        """Prepare corpus for LDA training."""
        self.logger.info(f"Preparing corpus from {len(processed_docs)} documents")
        
        # Create dictionary
        self.dictionary = corpora.Dictionary(processed_docs)
        
        # Filter extremes
        self.dictionary.filter_extremes(
            no_below=2,  # Minimum document frequency
            no_above=0.8,  # Maximum document frequency ratio
            keep_n=10000  # Keep top N most frequent words
        )
        
        # Create corpus
        self.corpus = [self.dictionary.doc2bow(doc) for doc in processed_docs]
        
        # Remove empty documents
        self.corpus = [doc for doc in self.corpus if doc]
        
        self.logger.info(f"Dictionary size: {len(self.dictionary)}")
        self.logger.info(f"Corpus size: {len(self.corpus)}")
        
        # Store metadata
        self.training_metadata.update({
            'dictionary_size': len(self.dictionary),
            'corpus_size': len(self.corpus),
            'preparation_time': datetime.now().isoformat()
        })
    
    def train(self, processed_docs: List[List[str]]) -> None:
        """Train LDA model."""
        self.logger.info("Starting LDA training")
        
        # Prepare corpus if not already done
        if self.corpus is None:
            self.prepare_corpus(processed_docs)
        
        # Initialize callback
        callback = LDATrainingCallback()
        
        # Train LDA model
        self.lda_model = LdaModel(
            corpus=self.corpus,
            id2word=self.dictionary,
            num_topics=self.config.num_topics,
            alpha=self.config.alpha,
            eta=self.config.beta,
            iterations=self.config.iterations,
            passes=self.config.passes,
            random_state=self.config.random_state,
            minimum_probability=self.config.minimum_probability,
            per_word_topics=self.config.per_word_topics,
            callbacks=[callback]
        )
        
        self.logger.info("LDA training completed")
        
        # Generate topic names
        self._generate_topic_names()
        
        # Calculate model coherence
        coherence_score = self._calculate_coherence(processed_docs)
        
        # Update metadata
        self.training_metadata.update({
            'num_topics': self.config.num_topics,
            'coherence_score': coherence_score,
            'training_time': datetime.now().isoformat()
        })
        
        self.logger.info(f"Model coherence score: {coherence_score:.4f}")
    
    def _generate_topic_names(self) -> None:
        """Generate human-readable topic names."""
        self.topic_names = {}
        
        for topic_id in range(self.config.num_topics):
            # Get top words for topic
            top_words = self.lda_model.show_topic(topic_id, topn=5)
            top_words_str = [word for word, _ in top_words]
            
            # Create topic name from top words
            topic_name = "_".join(top_words_str[:3])
            self.topic_names[topic_id] = topic_name
        
        self.logger.info(f"Generated topic names: {self.topic_names}")
    
    def _calculate_coherence(self, processed_docs: List[List[str]]) -> float:
        """Calculate model coherence."""
        try:
            coherence_model = CoherenceModel(
                model=self.lda_model,
                texts=processed_docs,
                dictionary=self.dictionary,
                coherence='c_v'
            )
            return coherence_model.get_coherence()
        except Exception as e:
            self.logger.warning(f"Could not calculate coherence: {str(e)}")
            return 0.0
    
    def get_document_topics(self, document: List[str]) -> List[Tuple[int, float]]:
        """Get topic distribution for a document."""
        if self.lda_model is None:
            raise ValueError("Model not trained yet")
        
        # Convert document to bow
        bow = self.dictionary.doc2bow(document)
        
        # Get topic distribution
        topic_dist = self.lda_model.get_document_topics(
            bow,
            minimum_probability=self.config.minimum_probability
        )
        
        return topic_dist
    
    def get_location_topic_signature(
        self,
        location_text: List[str],
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Get topic signature for a location."""
        topic_dist = self.get_document_topics(location_text)
        
        # Convert to dictionary format
        topic_signature = {}
        for topic_id, probability in topic_dist:
            topic_signature[topic_id] = {
                'probability': probability,
                'topic_name': self.topic_names.get(topic_id, f"topic_{topic_id}"),
                'top_words': [word for word, _ in self.lda_model.show_topic(topic_id, topn=5)]
            }
        
        # Add dominant topic
        if topic_dist:
            dominant_topic_id = max(topic_dist, key=lambda x: x[1])[0]
            topic_signature['dominant_topic'] = {
                'topic_id': dominant_topic_id,
                'probability': dict(topic_dist)[dominant_topic_id],
                'topic_name': self.topic_names.get(dominant_topic_id, f"topic_{dominant_topic_id}")
            }
        
        return topic_signature
    
    def get_topic_words(self, topic_id: int, topn: int = 10) -> List[Tuple[str, float]]:
        """Get top words for a topic."""
        if self.lda_model is None:
            raise ValueError("Model not trained yet")
        
        return self.lda_model.show_topic(topic_id, topn=topn)
    
    def get_all_topics_summary(self) -> Dict[int, Dict[str, Any]]:
        """Get summary of all topics."""
        if self.lda_model is None:
            raise ValueError("Model not trained yet")
        
        topics_summary = {}
        for topic_id in range(self.config.num_topics):
            top_words = self.get_topic_words(topic_id, topn=10)
            
            topics_summary[topic_id] = {
                'topic_name': self.topic_names.get(topic_id, f"topic_{topic_id}"),
                'top_words': top_words,
                'word_string': ' + '.join([f"{prob:.3f}*{word}" for word, prob in top_words[:5]])
            }
        
        return topics_summary
    
    def save_model(self, filepath: str) -> None:
        """Save trained model to file."""
        if self.lda_model is None:
            raise ValueError("No model to save")
        
        model_data = {
            'lda_model': self.lda_model,
            'dictionary': self.dictionary,
            'config': self.config,
            'topic_names': self.topic_names,
            'training_metadata': self.training_metadata
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load trained model from file."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.lda_model = model_data['lda_model']
        self.dictionary = model_data['dictionary']
        self.config = model_data['config']
        self.topic_names = model_data['topic_names']
        self.training_metadata = model_data['training_metadata']
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        if self.lda_model is None:
            return {"status": "not_trained"}
        
        return {
            "status": "trained",
            "num_topics": self.config.num_topics,
            "dictionary_size": len(self.dictionary) if self.dictionary else 0,
            "corpus_size": len(self.corpus) if self.corpus else 0,
            "topic_names": self.topic_names,
            "training_metadata": self.training_metadata
        }
