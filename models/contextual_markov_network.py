"""Contextual Markov Network for activity inference."""

import logging
import pickle
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime, timedelta
from sklearn.preprocessing import LabelEncoder
import networkx as nx

from config.model_config import CMNConfig


class ContextualMarkovNetwork:
    """Contextual Markov Network for activity inference."""
    
    def __init__(self, config: CMNConfig):
        """Initialize CMN."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model components
        self.activity_encoder = LabelEncoder()
        self.transition_matrix = None
        self.emission_matrix = None
        self.context_weights = None
        
        # Network representation
        self.network = nx.DiGraph()
        
        # Training metadata
        self.training_metadata = {}
        
        # Context feature extractors
        self.context_extractors = {
            'temporal': self._extract_temporal_features,
            'spatial': self._extract_spatial_features,
            'familiarity': self._extract_familiarity_features,
            'topic': self._extract_topic_features,
            'sequence': self._extract_sequence_features
        }
    
    def prepare_training_data(
        self,
        activity_sequences_df: pd.DataFrame,
        location_features_df: pd.DataFrame,
        user_metadata_df: pd.DataFrame,
        topic_signatures_df: Optional[pd.DataFrame] = None,
        topic_regimes_df: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """Prepare training data for CMN."""
        self.logger.info("Preparing training data for CMN")
        
        # Merge dataframes
        merged_df = activity_sequences_df.copy()
        
        # Add location features
        merged_df = merged_df.merge(
            location_features_df,
            on='smallest_circle_id',
            how='left'
        )
        
        # Add user metadata
        merged_df = merged_df.merge(
            user_metadata_df[['user_id', 'home_latitude', 'home_longitude', 
                             'work_latitude', 'work_longitude', 'familiar_locations']],
            on='user_id',
            how='left'
        )
        
        # Add topic information if available
        if topic_signatures_df is not None:
            merged_df = merged_df.merge(
                topic_signatures_df,
                on=['smallest_circle_id', 'timestamp'],
                how='left'
            )
        
        if topic_regimes_df is not None:
            merged_df = merged_df.merge(
                topic_regimes_df,
                on=['smallest_circle_id', 'timestamp'],
                how='left'
            )
        
        # Sort by user and timestamp
        merged_df = merged_df.sort_values(['user_id', 'timestamp'])
        
        # Extract sequences and contexts
        sequences = []
        contexts = []
        
        for user_id, user_group in merged_df.groupby('user_id'):
            user_sequence = []
            user_contexts = []
            
            for _, row in user_group.iterrows():
                # Extract activity
                activity = row['activity_type']
                if pd.isna(activity):
                    continue
                
                user_sequence.append(activity)
                
                # Extract context features
                context = self._extract_all_context_features(row, user_group)
                user_contexts.append(context)
            
            if len(user_sequence) > 1:  # Need at least 2 activities
                sequences.append(user_sequence)
                contexts.append(user_contexts)
        
        self.logger.info(f"Prepared {len(sequences)} user sequences")
        
        return {
            'sequences': sequences,
            'contexts': contexts,
            'merged_data': merged_df
        }
    
    def _extract_all_context_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, Any]:
        """Extract all context features for a single observation."""
        context = {}
        
        for feature_type, extractor in self.context_extractors.items():
            try:
                features = extractor(row, user_group)
                context[feature_type] = features
            except Exception as e:
                self.logger.warning(f"Failed to extract {feature_type} features: {str(e)}")
                context[feature_type] = {}
        
        return context
    
    def _extract_temporal_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, float]:
        """Extract temporal context features."""
        timestamp = pd.to_datetime(row['timestamp'])
        
        return {
            'hour_of_day': timestamp.hour / 24.0,
            'day_of_week': timestamp.dayofweek / 6.0,
            'day_of_month': (timestamp.day - 1) / 30.0,
            'month': (timestamp.month - 1) / 11.0,
            'is_weekend': float(timestamp.dayofweek >= 5),
            'is_business_hours': float(9 <= timestamp.hour <= 17),
            'hour_sin': np.sin(2 * np.pi * timestamp.hour / 24),
            'hour_cos': np.cos(2 * np.pi * timestamp.hour / 24),
            'day_sin': np.sin(2 * np.pi * timestamp.dayofweek / 7),
            'day_cos': np.cos(2 * np.pi * timestamp.dayofweek / 7)
        }
    
    def _extract_spatial_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, float]:
        """Extract spatial context features."""
        features = {}
        
        # Location characteristics
        features.update({
            'poi_density': row.get('poi_density', 0.0),
            'residential_ratio': row.get('residential_ratio', 0.0),
            'commercial_ratio': row.get('commercial_ratio', 0.0),
            'industrial_ratio': row.get('industrial_ratio', 0.0),
            'recreational_ratio': row.get('recreational_ratio', 0.0),
            'transportation_ratio': row.get('transportation_ratio', 0.0)
        })
        
        # Distance to home/work
        if not pd.isna(row.get('home_latitude')):
            home_dist = self._calculate_distance(
                row['latitude'], row['longitude'],
                row['home_latitude'], row['home_longitude']
            )
            features['distance_to_home'] = min(home_dist / 10000.0, 1.0)  # Normalize to 10km
        
        if not pd.isna(row.get('work_latitude')):
            work_dist = self._calculate_distance(
                row['latitude'], row['longitude'],
                row['work_latitude'], row['work_longitude']
            )
            features['distance_to_work'] = min(work_dist / 10000.0, 1.0)  # Normalize to 10km
        
        return features
    
    def _extract_familiarity_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, float]:
        """Extract familiarity context features."""
        features = {}
        
        # Check if location is in familiar locations
        familiar_locations = row.get('familiar_locations', [])
        current_circle = row['smallest_circle_id']
        
        is_familiar = False
        visit_frequency = 0.0
        avg_duration = 0.0
        
        if isinstance(familiar_locations, list):
            for loc in familiar_locations:
                if isinstance(loc, dict) and loc.get('smallest_circle_id') == current_circle:
                    is_familiar = True
                    visit_frequency = loc.get('visit_frequency', 0.0)
                    avg_duration = loc.get('avg_duration_minutes', 0.0) / 480.0  # Normalize to 8 hours
                    break
        
        features.update({
            'is_familiar_location': float(is_familiar),
            'visit_frequency': visit_frequency,
            'avg_duration_normalized': min(avg_duration, 1.0)
        })
        
        # Historical activity patterns at this location
        location_history = user_group[user_group['smallest_circle_id'] == current_circle]
        if len(location_history) > 1:
            activity_counts = location_history['activity_type'].value_counts(normalize=True)
            for activity in self.config.activity_types:
                features[f'historical_{activity}_prob'] = activity_counts.get(activity, 0.0)
        
        return features
    
    def _extract_topic_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, float]:
        """Extract topic-related context features."""
        features = {}
        
        # Topic signature features
        if 'topic_distribution' in row and not pd.isna(row['topic_distribution']):
            topic_dist = row['topic_distribution']
            if isinstance(topic_dist, (list, dict)):
                # Extract topic probabilities
                if isinstance(topic_dist, list):
                    for i, topic_prob in enumerate(topic_dist):
                        if isinstance(topic_prob, dict):
                            features[f'topic_{i}_prob'] = topic_prob.get('probability', 0.0)
                        else:
                            features[f'topic_{i}_prob'] = topic_prob
        
        # Topic regime features
        if 'regime_id' in row and not pd.isna(row['regime_id']):
            regime_id = int(row['regime_id'])
            regime_prob = row.get('regime_probability', 0.0)
            
            features.update({
                f'regime_{regime_id}': 1.0,
                'regime_confidence': regime_prob
            })
        
        return features
    
    def _extract_sequence_features(self, row: pd.Series, user_group: pd.DataFrame) -> Dict[str, float]:
        """Extract sequence context features."""
        features = {}
        
        current_idx = user_group.index.get_loc(row.name)
        
        # Previous activity
        if current_idx > 0:
            prev_activity = user_group.iloc[current_idx - 1]['activity_type']
            if not pd.isna(prev_activity):
                for activity in self.config.activity_types:
                    features[f'prev_activity_{activity}'] = float(prev_activity == activity)
        
        # Duration at current location
        duration = row.get('duration_minutes', 0)
        features['duration_normalized'] = min(duration / 480.0, 1.0)  # Normalize to 8 hours
        
        return features
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points in meters."""
        from math import radians, cos, sin, asin, sqrt
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # Earth radius in meters
        
        return c * r
    
    def train(self, training_data: Dict[str, Any]) -> None:
        """Train the Contextual Markov Network."""
        self.logger.info("Starting CMN training")
        
        sequences = training_data['sequences']
        contexts = training_data['contexts']
        
        # Encode activities
        all_activities = []
        for seq in sequences:
            all_activities.extend(seq)
        
        self.activity_encoder.fit(all_activities)
        n_activities = len(self.activity_encoder.classes_)
        
        self.logger.info(f"Training on {len(sequences)} sequences with {n_activities} activity types")
        
        # Initialize matrices
        self.transition_matrix = np.zeros((n_activities, n_activities))
        
        # Extract context feature dimensions
        sample_context = contexts[0][0] if contexts else {}
        context_dim = self._get_context_dimension(sample_context)
        
        # Learn transition probabilities with context
        self._learn_transitions(sequences, contexts)
        
        # Build network representation
        self._build_network()
        
        # Update metadata
        self.training_metadata.update({
            'n_activities': n_activities,
            'activity_types': list(self.activity_encoder.classes_),
            'context_dimension': context_dim,
            'n_sequences': len(sequences),
            'training_time': datetime.now().isoformat()
        })
        
        self.logger.info("CMN training completed")
    
    def _get_context_dimension(self, sample_context: Dict[str, Any]) -> int:
        """Get total context feature dimension."""
        total_dim = 0
        for feature_type, features in sample_context.items():
            if isinstance(features, dict):
                total_dim += len(features)
        return total_dim
    
    def _learn_transitions(self, sequences: List[List[str]], contexts: List[List[Dict]]) -> None:
        """Learn context-dependent transition probabilities."""
        # Count transitions
        for seq, seq_contexts in zip(sequences, contexts):
            encoded_seq = self.activity_encoder.transform(seq)
            
            for i in range(len(encoded_seq) - 1):
                current_activity = encoded_seq[i]
                next_activity = encoded_seq[i + 1]
                
                # Add transition count
                self.transition_matrix[current_activity, next_activity] += 1
        
        # Normalize to probabilities
        row_sums = self.transition_matrix.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1  # Avoid division by zero
        self.transition_matrix = self.transition_matrix / row_sums
        
        # Add smoothing
        self.transition_matrix += self.config.transition_smoothing
        self.transition_matrix = self.transition_matrix / self.transition_matrix.sum(axis=1, keepdims=True)
    
    def _build_network(self) -> None:
        """Build NetworkX representation of the CMN."""
        self.network.clear()
        
        # Add nodes (activities)
        for i, activity in enumerate(self.activity_encoder.classes_):
            self.network.add_node(activity, activity_id=i)
        
        # Add edges (transitions)
        for i, from_activity in enumerate(self.activity_encoder.classes_):
            for j, to_activity in enumerate(self.activity_encoder.classes_):
                weight = self.transition_matrix[i, j]
                if weight > 0:
                    self.network.add_edge(
                        from_activity, 
                        to_activity, 
                        weight=weight,
                        transition_prob=weight
                    )
    
    def predict_activity(
        self,
        context: Dict[str, Any],
        previous_activity: Optional[str] = None,
        top_k: int = 3
    ) -> List[Dict[str, Any]]:
        """Predict activity given context."""
        if self.transition_matrix is None:
            raise ValueError("Model not trained yet")
        
        # Get base probabilities
        if previous_activity and previous_activity in self.activity_encoder.classes_:
            prev_idx = self.activity_encoder.transform([previous_activity])[0]
            base_probs = self.transition_matrix[prev_idx, :]
        else:
            # Uniform prior if no previous activity
            base_probs = np.ones(len(self.activity_encoder.classes_)) / len(self.activity_encoder.classes_)
        
        # Apply context weighting (simplified version)
        context_adjusted_probs = base_probs.copy()
        
        # Get top-k predictions
        top_indices = np.argsort(context_adjusted_probs)[-top_k:][::-1]
        
        predictions = []
        for idx in top_indices:
            activity = self.activity_encoder.classes_[idx]
            probability = context_adjusted_probs[idx]
            
            predictions.append({
                'activity': activity,
                'probability': probability,
                'confidence': probability / np.sum(context_adjusted_probs[top_indices])
            })
        
        return predictions
    
    def save_model(self, filepath: str) -> None:
        """Save trained model to file."""
        if self.transition_matrix is None:
            raise ValueError("No model to save")
        
        model_data = {
            'activity_encoder': self.activity_encoder,
            'transition_matrix': self.transition_matrix,
            'emission_matrix': self.emission_matrix,
            'context_weights': self.context_weights,
            'network': self.network,
            'config': self.config,
            'training_metadata': self.training_metadata
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load trained model from file."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.activity_encoder = model_data['activity_encoder']
        self.transition_matrix = model_data['transition_matrix']
        self.emission_matrix = model_data['emission_matrix']
        self.context_weights = model_data['context_weights']
        self.network = model_data['network']
        self.config = model_data['config']
        self.training_metadata = model_data['training_metadata']
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        if self.transition_matrix is None:
            return {"status": "not_trained"}
        
        return {
            "status": "trained",
            "n_activities": len(self.activity_encoder.classes_),
            "activity_types": list(self.activity_encoder.classes_),
            "network_nodes": self.network.number_of_nodes(),
            "network_edges": self.network.number_of_edges(),
            "training_metadata": self.training_metadata
        }
