"""Hidden Markov Model for location-topic regime detection."""

import logging
import pickle
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime
from hmmlearn import hmm
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

from config.model_config import HMMConfig


class LocationTopicRegimeHMM:
    """HMM for detecting location-topic regimes."""
    
    def __init__(self, config: HMMConfig):
        """Initialize HMM model."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model components
        self.hmm_model = None
        self.scaler = StandardScaler()
        self.regime_names = None
        
        # Training metadata
        self.training_metadata = {}
        
        # Feature names for interpretation
        self.feature_names = []
    
    def prepare_sequences(
        self,
        topic_signatures_df: pd.DataFrame,
        location_id_col: str = 'smallest_circle_id',
        timestamp_col: str = 'timestamp',
        topic_dist_col: str = 'topic_distribution'
    ) -> Dict[str, np.ndarray]:
        """Prepare sequences for HMM training."""
        self.logger.info("Preparing sequences for HMM training")
        
        # Sort by location and timestamp
        df_sorted = topic_signatures_df.sort_values([location_id_col, timestamp_col])
        
        # Extract topic distributions
        sequences = {}
        
        for location_id, group in df_sorted.groupby(location_id_col):
            # Extract topic probabilities
            topic_probs = []
            timestamps = []
            
            for _, row in group.iterrows():
                # Parse topic distribution (assuming it's a list of dicts or similar)
                topic_dist = row[topic_dist_col]
                
                if isinstance(topic_dist, str):
                    # If stored as string, parse it
                    import ast
                    topic_dist = ast.literal_eval(topic_dist)
                
                # Convert to probability vector
                prob_vector = self._extract_probability_vector(topic_dist)
                
                if prob_vector is not None:
                    topic_probs.append(prob_vector)
                    timestamps.append(row[timestamp_col])
            
            if len(topic_probs) > 1:  # Need at least 2 observations
                sequences[location_id] = {
                    'observations': np.array(topic_probs),
                    'timestamps': timestamps
                }
        
        self.logger.info(f"Prepared sequences for {len(sequences)} locations")
        return sequences
    
    def _extract_probability_vector(self, topic_dist: Any) -> Optional[np.ndarray]:
        """Extract probability vector from topic distribution."""
        try:
            if isinstance(topic_dist, list):
                # List of (topic_id, probability) tuples
                max_topic_id = max([item['topic_id'] if isinstance(item, dict) 
                                  else item[0] for item in topic_dist])
                prob_vector = np.zeros(max_topic_id + 1)
                
                for item in topic_dist:
                    if isinstance(item, dict):
                        topic_id = item['topic_id']
                        probability = item['probability']
                    else:
                        topic_id, probability = item
                    prob_vector[topic_id] = probability
                
                return prob_vector
            
            elif isinstance(topic_dist, dict):
                # Dictionary format
                max_topic_id = max(topic_dist.keys())
                prob_vector = np.zeros(max_topic_id + 1)
                
                for topic_id, prob_info in topic_dist.items():
                    if isinstance(prob_info, dict):
                        probability = prob_info.get('probability', 0)
                    else:
                        probability = prob_info
                    prob_vector[topic_id] = probability
                
                return prob_vector
            
            else:
                return None
                
        except Exception as e:
            self.logger.warning(f"Could not parse topic distribution: {str(e)}")
            return None
    
    def add_temporal_features(self, sequences: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Add temporal features to sequences."""
        enhanced_sequences = {}
        
        for location_id, seq_data in sequences.items():
            observations = seq_data['observations']
            timestamps = seq_data['timestamps']
            
            # Convert timestamps to pandas datetime
            ts_series = pd.to_datetime(timestamps)
            
            # Extract temporal features
            temporal_features = np.column_stack([
                ts_series.hour / 24.0,  # Hour of day (normalized)
                ts_series.dayofweek / 6.0,  # Day of week (normalized)
                (ts_series.day - 1) / 30.0,  # Day of month (normalized)
                np.sin(2 * np.pi * ts_series.hour / 24),  # Cyclical hour
                np.cos(2 * np.pi * ts_series.hour / 24),
                np.sin(2 * np.pi * ts_series.dayofweek / 7),  # Cyclical day
                np.cos(2 * np.pi * ts_series.dayofweek / 7)
            ])
            
            # Combine topic probabilities with temporal features
            enhanced_obs = np.column_stack([observations, temporal_features])
            
            enhanced_sequences[location_id] = {
                'observations': enhanced_obs,
                'timestamps': timestamps
            }
        
        # Update feature names
        num_topics = observations.shape[1]
        self.feature_names = [f'topic_{i}' for i in range(num_topics)]
        self.feature_names.extend([
            'hour_norm', 'day_norm', 'date_norm',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos'
        ])
        
        return enhanced_sequences
    
    def train(self, sequences: Dict[str, np.ndarray]) -> None:
        """Train HMM model."""
        self.logger.info("Starting HMM training")
        
        # Add temporal features
        enhanced_sequences = self.add_temporal_features(sequences)
        
        # Combine all sequences for training
        all_observations = []
        sequence_lengths = []
        
        for location_id, seq_data in enhanced_sequences.items():
            obs = seq_data['observations']
            all_observations.append(obs)
            sequence_lengths.append(len(obs))
        
        # Concatenate all observations
        X = np.vstack(all_observations)
        
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Initialize HMM model
        self.hmm_model = hmm.GaussianHMM(
            n_components=self.config.n_components,
            covariance_type=self.config.covariance_type,
            n_iter=self.config.n_iter,
            tol=self.config.tol,
            random_state=self.config.random_state,
            algorithm=self.config.algorithm
        )
        
        # Train model
        self.hmm_model.fit(X_scaled, sequence_lengths)
        
        self.logger.info("HMM training completed")
        
        # Generate regime names
        self._generate_regime_names(X_scaled, sequence_lengths)
        
        # Calculate model score
        model_score = self.hmm_model.score(X_scaled, sequence_lengths)
        
        # Update metadata
        self.training_metadata.update({
            'n_components': self.config.n_components,
            'model_score': model_score,
            'num_sequences': len(sequences),
            'total_observations': len(X),
            'feature_dim': X.shape[1],
            'training_time': datetime.now().isoformat()
        })
        
        self.logger.info(f"Model score: {model_score:.4f}")
    
    def _generate_regime_names(self, X: np.ndarray, lengths: List[int]) -> None:
        """Generate human-readable regime names."""
        # Predict states for all observations
        states = self.hmm_model.predict(X, lengths)
        
        # Analyze characteristics of each state
        self.regime_names = {}
        
        for state_id in range(self.config.n_components):
            state_mask = states == state_id
            state_observations = X[state_mask]
            
            if len(state_observations) == 0:
                self.regime_names[state_id] = f"regime_{state_id}"
                continue
            
            # Calculate mean features for this state
            mean_features = np.mean(state_observations, axis=0)
            
            # Analyze temporal patterns
            hour_norm = mean_features[self.feature_names.index('hour_norm')]
            day_norm = mean_features[self.feature_names.index('day_norm')]
            
            # Analyze topic patterns (first few topics)
            topic_means = mean_features[:len([f for f in self.feature_names if f.startswith('topic_')])]
            dominant_topic = np.argmax(topic_means)
            
            # Generate name based on patterns
            hour_of_day = int(hour_norm * 24)
            day_of_week = int(day_norm * 7)
            
            time_label = self._get_time_label(hour_of_day, day_of_week)
            topic_label = f"topic_{dominant_topic}"
            
            regime_name = f"{time_label}_{topic_label}"
            self.regime_names[state_id] = regime_name
        
        self.logger.info(f"Generated regime names: {self.regime_names}")
    
    def _get_time_label(self, hour: int, day: int) -> str:
        """Get time-based label."""
        if day in [5, 6]:  # Weekend
            if 6 <= hour < 12:
                return "weekend_morning"
            elif 12 <= hour < 18:
                return "weekend_afternoon"
            elif 18 <= hour < 24:
                return "weekend_evening"
            else:
                return "weekend_night"
        else:  # Weekday
            if 6 <= hour < 9:
                return "weekday_morning"
            elif 9 <= hour < 17:
                return "weekday_business"
            elif 17 <= hour < 21:
                return "weekday_evening"
            else:
                return "weekday_night"
    
    def predict_regime(
        self,
        topic_signature: np.ndarray,
        timestamp: datetime
    ) -> Dict[str, Any]:
        """Predict regime for a single observation."""
        if self.hmm_model is None:
            raise ValueError("Model not trained yet")
        
        # Add temporal features
        ts = pd.to_datetime(timestamp)
        temporal_features = np.array([
            ts.hour / 24.0,
            ts.dayofweek / 6.0,
            (ts.day - 1) / 30.0,
            np.sin(2 * np.pi * ts.hour / 24),
            np.cos(2 * np.pi * ts.hour / 24),
            np.sin(2 * np.pi * ts.dayofweek / 7),
            np.cos(2 * np.pi * ts.dayofweek / 7)
        ])
        
        # Combine features
        features = np.concatenate([topic_signature, temporal_features]).reshape(1, -1)
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Predict state probabilities
        state_probs = self.hmm_model.predict_proba(features_scaled)[0]
        
        # Get most likely state
        most_likely_state = np.argmax(state_probs)
        
        return {
            'regime_id': most_likely_state,
            'regime_name': self.regime_names.get(most_likely_state, f"regime_{most_likely_state}"),
            'regime_probability': state_probs[most_likely_state],
            'all_regime_probabilities': {
                i: {
                    'probability': prob,
                    'regime_name': self.regime_names.get(i, f"regime_{i}")
                }
                for i, prob in enumerate(state_probs)
            }
        }
    
    def save_model(self, filepath: str) -> None:
        """Save trained model to file."""
        if self.hmm_model is None:
            raise ValueError("No model to save")
        
        model_data = {
            'hmm_model': self.hmm_model,
            'scaler': self.scaler,
            'config': self.config,
            'regime_names': self.regime_names,
            'feature_names': self.feature_names,
            'training_metadata': self.training_metadata
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load trained model from file."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.hmm_model = model_data['hmm_model']
        self.scaler = model_data['scaler']
        self.config = model_data['config']
        self.regime_names = model_data['regime_names']
        self.feature_names = model_data['feature_names']
        self.training_metadata = model_data['training_metadata']
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        if self.hmm_model is None:
            return {"status": "not_trained"}
        
        return {
            "status": "trained",
            "n_components": self.config.n_components,
            "feature_dim": len(self.feature_names),
            "regime_names": self.regime_names,
            "training_metadata": self.training_metadata
        }
