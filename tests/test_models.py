"""Tests for DCMN models."""

import unittest
import numpy as np
import pandas as pd
from datetime import datetime

from config.model_config import LDAConfig, HMMConfig, CMNConfig
from models.lda_model import LocationLDAModel
from models.hmm_model import LocationTopicRegimeHMM
from models.contextual_markov_network import ContextualMarkovNetwork
from data.preprocessing.text_processor import TextProcessor


class TestLDAModel(unittest.TestCase):
    """Test LDA model functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = LDAConfig(num_topics=3, iterations=10, passes=2)
        self.model = LocationLDAModel(self.config)
        self.text_processor = TextProcessor()
        
        # Sample documents
        self.sample_texts = [
            "restaurant food dining cuisine meal lunch dinner",
            "park recreation outdoor nature green space walking",
            "office building business work professional commercial",
            "shopping mall retail store market grocery",
            "hospital healthcare medical clinic doctor treatment"
        ]
    
    def test_text_processing(self):
        """Test text preprocessing."""
        processed_docs = self.text_processor.process_corpus(self.sample_texts)
        
        self.assertIsInstance(processed_docs, list)
        self.assertGreater(len(processed_docs), 0)
        
        # Check that documents are tokenized
        for doc in processed_docs:
            self.assertIsInstance(doc, list)
            self.assertGreater(len(doc), 0)
    
    def test_lda_training(self):
        """Test LDA model training."""
        processed_docs = self.text_processor.process_corpus(self.sample_texts)
        
        # Train model
        self.model.train(processed_docs)
        
        # Check model is trained
        self.assertIsNotNone(self.model.lda_model)
        self.assertIsNotNone(self.model.dictionary)
        self.assertIsNotNone(self.model.corpus)
        
        # Check topic names are generated
        self.assertIsNotNone(self.model.topic_names)
        self.assertEqual(len(self.model.topic_names), self.config.num_topics)
    
    def test_topic_signature_generation(self):
        """Test topic signature generation."""
        processed_docs = self.text_processor.process_corpus(self.sample_texts)
        self.model.train(processed_docs)
        
        # Test document
        test_doc = ["restaurant", "food", "dining"]
        signature = self.model.get_location_topic_signature(test_doc)
        
        self.assertIsInstance(signature, dict)
        self.assertIn('dominant_topic', signature)


class TestHMMModel(unittest.TestCase):
    """Test HMM model functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = HMMConfig(n_components=3, n_iter=10)
        self.model = LocationTopicRegimeHMM(self.config)
        
        # Sample topic signatures data
        self.sample_data = pd.DataFrame({
            'smallest_circle_id': ['loc1', 'loc1', 'loc1', 'loc2', 'loc2'],
            'timestamp': [
                '2024-01-01 09:00:00',
                '2024-01-01 12:00:00',
                '2024-01-01 18:00:00',
                '2024-01-01 10:00:00',
                '2024-01-01 15:00:00'
            ],
            'topic_distribution': [
                [{'topic_id': 0, 'probability': 0.7}, {'topic_id': 1, 'probability': 0.3}],
                [{'topic_id': 0, 'probability': 0.4}, {'topic_id': 1, 'probability': 0.6}],
                [{'topic_id': 0, 'probability': 0.2}, {'topic_id': 1, 'probability': 0.8}],
                [{'topic_id': 0, 'probability': 0.8}, {'topic_id': 1, 'probability': 0.2}],
                [{'topic_id': 0, 'probability': 0.5}, {'topic_id': 1, 'probability': 0.5}]
            ]
        })
    
    def test_sequence_preparation(self):
        """Test sequence preparation for HMM."""
        sequences = self.model.prepare_sequences(self.sample_data)
        
        self.assertIsInstance(sequences, dict)
        self.assertGreater(len(sequences), 0)
        
        # Check sequence structure
        for location_id, seq_data in sequences.items():
            self.assertIn('observations', seq_data)
            self.assertIn('timestamps', seq_data)
            self.assertIsInstance(seq_data['observations'], np.ndarray)
    
    def test_hmm_training(self):
        """Test HMM model training."""
        sequences = self.model.prepare_sequences(self.sample_data)
        
        if sequences:  # Only test if we have valid sequences
            self.model.train(sequences)
            
            # Check model is trained
            self.assertIsNotNone(self.model.hmm_model)
            self.assertIsNotNone(self.model.scaler)
            self.assertIsNotNone(self.model.regime_names)


class TestCMNModel(unittest.TestCase):
    """Test Contextual Markov Network functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = CMNConfig()
        self.model = ContextualMarkovNetwork(self.config)
        
        # Sample training data
        self.activity_sequences = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user1', 'user2', 'user2'],
            'timestamp': [
                '2024-01-01 09:00:00',
                '2024-01-01 12:00:00',
                '2024-01-01 18:00:00',
                '2024-01-01 10:00:00',
                '2024-01-01 15:00:00'
            ],
            'activity_type': ['home', 'work', 'home', 'home', 'shopping'],
            'latitude': [40.7589, 40.7614, 40.7589, 40.7282, 40.7505],
            'longitude': [-73.9851, -73.9776, -73.9851, -73.7949, -73.9934],
            'smallest_circle_id': ['circle1', 'circle2', 'circle1', 'circle3', 'circle4']
        })
        
        self.location_features = pd.DataFrame({
            'smallest_circle_id': ['circle1', 'circle2', 'circle3', 'circle4'],
            'poi_density': [0.1, 0.3, 0.2, 0.4],
            'residential_ratio': [0.8, 0.1, 0.7, 0.3],
            'commercial_ratio': [0.1, 0.7, 0.2, 0.6]
        })
        
        self.user_metadata = pd.DataFrame({
            'user_id': ['user1', 'user2'],
            'home_latitude': [40.7589, 40.7282],
            'home_longitude': [-73.9851, -73.7949],
            'familiar_locations': [[], []]
        })
    
    def test_training_data_preparation(self):
        """Test training data preparation."""
        training_data = self.model.prepare_training_data(
            self.activity_sequences,
            self.location_features,
            self.user_metadata
        )
        
        self.assertIsInstance(training_data, dict)
        self.assertIn('sequences', training_data)
        self.assertIn('contexts', training_data)
        
        # Check sequences structure
        sequences = training_data['sequences']
        self.assertIsInstance(sequences, list)
        self.assertGreater(len(sequences), 0)
    
    def test_cmn_training(self):
        """Test CMN model training."""
        training_data = self.model.prepare_training_data(
            self.activity_sequences,
            self.location_features,
            self.user_metadata
        )
        
        self.model.train(training_data)
        
        # Check model is trained
        self.assertIsNotNone(self.model.transition_matrix)
        self.assertIsNotNone(self.model.activity_encoder)
        
        # Check transition matrix shape
        n_activities = len(self.model.activity_encoder.classes_)
        self.assertEqual(self.model.transition_matrix.shape, (n_activities, n_activities))
    
    def test_activity_prediction(self):
        """Test activity prediction."""
        training_data = self.model.prepare_training_data(
            self.activity_sequences,
            self.location_features,
            self.user_metadata
        )
        
        self.model.train(training_data)
        
        # Test prediction
        context = {
            'temporal': {'hour_of_day': 0.5, 'is_weekend': 0.0},
            'spatial': {'poi_density': 0.3, 'commercial_ratio': 0.6}
        }
        
        predictions = self.model.predict_activity(context, top_k=3)
        
        self.assertIsInstance(predictions, list)
        self.assertLessEqual(len(predictions), 3)
        
        # Check prediction structure
        for pred in predictions:
            self.assertIn('activity', pred)
            self.assertIn('probability', pred)
            self.assertIn('confidence', pred)


if __name__ == '__main__':
    unittest.main()
