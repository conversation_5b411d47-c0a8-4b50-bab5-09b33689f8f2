"""End-to-end example of DCMN framework usage."""

import os
import sys
import logging
from datetime import datetime, timedelta
import pandas as pd

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.bigquery_config import BigQueryConfig
from config.neo4j_config import Neo4jConfig
from config.model_config import ModelConfig
from pipeline.training_pipeline import DCMNTrainingPipeline
from utils.logging_config import setup_logging


def create_sample_config():
    """Create a sample configuration file."""
    config = {
        'data': {
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'min_confidence': 0.7
        },
        'models': {
            'lda': {
                'num_topics': 8,
                'iterations': 500,
                'passes': 5
            },
            'hmm': {
                'n_components': 4,
                'n_iter': 50
            },
            'cmn': {
                'max_familiar_locations': 20,
                'spatial_threshold_meters': 150.0
            }
        },
        'output': {
            'model_dir': './example_models',
            'save_models': True
        }
    }
    
    import yaml
    with open('example_config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    return 'example_config.yaml'


def setup_environment():
    """Setup environment variables for the example."""
    # Set environment variables (you would set these in your actual environment)
    os.environ.setdefault('BIGQUERY_PROJECT_ID', 'your-project-id')
    os.environ.setdefault('BIGQUERY_DATASET_ID', 'dcmn_dataset')
    os.environ.setdefault('NEO4J_URI', 'bolt://localhost:7687')
    os.environ.setdefault('NEO4J_USERNAME', 'neo4j')
    os.environ.setdefault('NEO4J_PASSWORD', 'your-password')


def run_training_example():
    """Run a complete training example."""
    print("=" * 60)
    print("DCMN Framework - End-to-End Training Example")
    print("=" * 60)
    
    # Setup logging
    setup_logging(level="INFO")
    logger = logging.getLogger(__name__)
    
    try:
        # Setup environment
        setup_environment()
        
        # Create sample configuration
        config_path = create_sample_config()
        logger.info(f"Created sample configuration: {config_path}")
        
        # Initialize and run training pipeline
        logger.info("Initializing training pipeline...")
        pipeline = DCMNTrainingPipeline(config_path)
        
        logger.info("Starting training pipeline...")
        result = pipeline.run_complete_pipeline()
        
        # Display results
        print("\n" + "=" * 60)
        print("TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print(f"\nTraining Status: {result.get('status', 'unknown')}")
        print(f"Start Time: {result.get('start_time', 'unknown')}")
        print(f"End Time: {result.get('end_time', 'unknown')}")
        
        if 'data_stats' in result:
            print(f"\nData Statistics:")
            for key, value in result['data_stats'].items():
                print(f"  {key}: {value}")
        
        if 'model_stats' in result:
            print(f"\nModel Statistics:")
            for model_name, stats in result['model_stats'].items():
                print(f"  {model_name}: {stats.get('status', 'unknown')}")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        print(f"\nERROR: {str(e)}")
        print("Please check the logs for more details.")
        return False
    
    return True


def run_inference_example():
    """Run an inference example."""
    print("\n" + "=" * 60)
    print("DCMN Framework - Inference Example")
    print("=" * 60)
    
    # This would demonstrate how to use trained models for inference
    print("Inference example would demonstrate:")
    print("1. Loading trained models")
    print("2. Preparing context features")
    print("3. Predicting activities")
    print("4. Querying Neo4j for network analysis")
    
    # Example context
    sample_context = {
        'temporal': {
            'hour_of_day': 0.5,  # 12:00 PM
            'day_of_week': 0.0,  # Monday
            'is_weekend': 0.0
        },
        'spatial': {
            'poi_density': 0.3,
            'commercial_ratio': 0.6,
            'distance_to_home': 0.1
        },
        'familiarity': {
            'is_familiar_location': 1.0,
            'visit_frequency': 0.8
        },
        'topic': {
            'topic_0_prob': 0.1,
            'topic_1_prob': 0.7,  # Dining topic
            'topic_2_prob': 0.2
        }
    }
    
    print(f"\nSample context features: {sample_context}")
    print("Predicted activity: dining (probability: 0.85)")
    print("Confidence: high")


def main():
    """Main function to run examples."""
    print("DCMN Framework Examples")
    print("Choose an example to run:")
    print("1. Complete training pipeline")
    print("2. Inference example")
    print("3. Both")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        run_training_example()
    elif choice == '2':
        run_inference_example()
    elif choice == '3':
        success = run_training_example()
        if success:
            run_inference_example()
    else:
        print("Invalid choice. Please run again and select 1, 2, or 3.")


if __name__ == '__main__':
    main()
