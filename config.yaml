# DCMN Framework Configuration File

# Data configuration
data:
  start_date: "2024-01-01"
  end_date: "2024-01-31"
  min_confidence: 0.7
  data_sources: ["osm_tags", "social_media", "reviews"]

# Model configurations
models:
  # LDA Topic Modeling
  lda:
    num_topics: 10
    alpha: "auto"
    beta: "auto"
    iterations: 1000
    passes: 10
    random_state: 42
    minimum_probability: 0.01
    per_word_topics: true

  # Hidden Markov Model
  hmm:
    n_components: 5
    covariance_type: "full"
    n_iter: 100
    tol: 0.01
    random_state: 42
    algorithm: "viterbi"

  # Contextual Markov Network
  cmn:
    activity_types:
      - "home"
      - "work"
      - "shopping"
      - "dining"
      - "recreation"
      - "transportation"
      - "social"
      - "healthcare"
      - "education"
      - "other"
    max_familiar_locations: 25
    spatial_threshold_meters: 100.0
    temporal_window_hours: 24
    transition_smoothing: 0.1
    emission_smoothing: 0.1

  # Optimization
  optimization:
    solver: "PULP_CBC_CMD"
    time_limit_seconds: 300
    gap_tolerance: 0.01
    threads: 4

# Output configuration
output:
  model_dir: "./models"
  save_models: true
  log_level: "INFO"
  export_results: true

# BigQuery configuration (can be overridden by environment variables)
bigquery:
  project_id: "your-project-id"
  dataset_id: "dcmn_dataset"
  location: "US"

# Neo4j configuration (can be overridden by environment variables)
neo4j:
  uri: "bolt://localhost:7687"
  username: "neo4j"
  password: "password"
  database: "neo4j"

# Processing configuration
processing:
  batch_size: 1000
  parallel_workers: 4
  memory_limit_gb: 8
  
# Text processing configuration
text_processing:
  language: "en"
  use_spacy: true
  min_word_frequency: 2
  max_word_frequency_ratio: 0.8
  time_window: "D"  # Daily aggregation

# Evaluation configuration
evaluation:
  test_split: 0.2
  validation_split: 0.1
  cross_validation_folds: 5
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
