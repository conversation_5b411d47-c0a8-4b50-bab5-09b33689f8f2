"""Text preprocessing for LDA topic modeling."""

import re
import logging
import pandas as pd
from typing import List, Dict, Set, Optional
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from nltk.tag import pos_tag
import spacy
from gensim.utils import simple_preprocess
from gensim.parsing.preprocessing import STOPWORDS


class TextProcessor:
    """Text preprocessing for location-based textual data."""
    
    def __init__(self, language: str = "en", use_spacy: bool = True):
        """Initialize text processor."""
        self.language = language
        self.use_spacy = use_spacy
        self.logger = logging.getLogger(__name__)
        
        # Download required NLTK data
        self._download_nltk_data()
        
        # Initialize NLTK components
        self.stop_words = set(stopwords.words('english'))
        self.lemmatizer = WordNetLemmatizer()
        
        # Initialize spaCy if requested
        if use_spacy:
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                self.logger.warning("spaCy model not found, falling back to NLTK")
                self.use_spacy = False
                self.nlp = None
        
        # Location-specific stop words
        self.location_stopwords = {
            'location', 'place', 'area', 'spot', 'site', 'venue', 'building',
            'street', 'road', 'avenue', 'boulevard', 'lane', 'way', 'drive',
            'north', 'south', 'east', 'west', 'center', 'centre', 'downtown',
            'near', 'close', 'nearby', 'around', 'within', 'inside', 'outside'
        }
        
        # Activity-relevant keywords to preserve
        self.preserve_keywords = {
            'restaurant', 'cafe', 'shop', 'store', 'market', 'mall', 'hospital',
            'school', 'university', 'park', 'gym', 'theater', 'cinema', 'museum',
            'library', 'bank', 'office', 'hotel', 'bar', 'club', 'church',
            'station', 'airport', 'pharmacy', 'clinic', 'salon', 'spa'
        }
    
    def _download_nltk_data(self) -> None:
        """Download required NLTK data."""
        required_data = ['punkt', 'stopwords', 'wordnet', 'averaged_perceptron_tagger']
        
        for data in required_data:
            try:
                nltk.data.find(f'tokenizers/{data}')
            except LookupError:
                self.logger.info(f"Downloading NLTK data: {data}")
                nltk.download(data, quiet=True)
    
    def clean_text(self, text: str) -> str:
        """Basic text cleaning."""
        if not isinstance(text, str):
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces and hyphens
        text = re.sub(r'[^a-zA-Z\s\-]', ' ', text)
        
        # Replace multiple spaces with single space
        text = re.sub(r'\s+', ' ', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def tokenize_and_filter(self, text: str) -> List[str]:
        """Tokenize text and apply filtering."""
        if self.use_spacy and self.nlp:
            return self._tokenize_spacy(text)
        else:
            return self._tokenize_nltk(text)
    
    def _tokenize_spacy(self, text: str) -> List[str]:
        """Tokenize using spaCy."""
        doc = self.nlp(text)
        
        tokens = []
        for token in doc:
            # Skip if token is stop word, punctuation, or space
            if (token.is_stop or token.is_punct or token.is_space or 
                len(token.text) < 2):
                continue
            
            # Skip location-specific stop words unless they're preserve keywords
            if (token.lemma_ in self.location_stopwords and 
                token.lemma_ not in self.preserve_keywords):
                continue
            
            # Only keep nouns, adjectives, and verbs
            if token.pos_ in ['NOUN', 'ADJ', 'VERB']:
                tokens.append(token.lemma_)
        
        return tokens
    
    def _tokenize_nltk(self, text: str) -> List[str]:
        """Tokenize using NLTK."""
        # Tokenize
        tokens = word_tokenize(text)
        
        # POS tagging
        pos_tags = pos_tag(tokens)
        
        # Filter tokens
        filtered_tokens = []
        for token, pos in pos_tags:
            # Skip short tokens
            if len(token) < 2:
                continue
            
            # Skip stop words
            if token in self.stop_words or token in STOPWORDS:
                continue
            
            # Skip location stop words unless preserve keywords
            if token in self.location_stopwords and token not in self.preserve_keywords:
                continue
            
            # Only keep nouns, adjectives, and verbs
            if pos.startswith(('NN', 'JJ', 'VB')):
                # Lemmatize
                lemmatized = self.lemmatizer.lemmatize(token, pos='n')
                if pos.startswith('VB'):
                    lemmatized = self.lemmatizer.lemmatize(lemmatized, pos='v')
                elif pos.startswith('JJ'):
                    lemmatized = self.lemmatizer.lemmatize(lemmatized, pos='a')
                
                filtered_tokens.append(lemmatized)
        
        return filtered_tokens
    
    def process_document(self, text: str) -> List[str]:
        """Process a single document."""
        # Clean text
        cleaned_text = self.clean_text(text)
        
        if not cleaned_text:
            return []
        
        # Tokenize and filter
        tokens = self.tokenize_and_filter(cleaned_text)
        
        return tokens
    
    def process_corpus(self, texts: List[str]) -> List[List[str]]:
        """Process a corpus of documents."""
        processed_docs = []
        
        for i, text in enumerate(texts):
            if i % 1000 == 0:
                self.logger.info(f"Processing document {i}/{len(texts)}")
            
            processed_doc = self.process_document(text)
            if processed_doc:  # Only add non-empty documents
                processed_docs.append(processed_doc)
        
        self.logger.info(f"Processed {len(processed_docs)} documents from {len(texts)} input texts")
        return processed_docs
    
    def create_location_documents(
        self,
        df: pd.DataFrame,
        text_column: str = 'text_content',
        location_column: str = 'smallest_circle_id',
        time_window: str = 'D'  # Daily aggregation
    ) -> Dict[str, List[str]]:
        """Create location-based documents for LDA."""
        # Convert timestamp to datetime if needed
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['time_period'] = df['timestamp'].dt.floor(time_window)
        
        # Group by location and time period
        location_docs = {}
        
        if 'time_period' in df.columns:
            grouped = df.groupby([location_column, 'time_period'])
        else:
            grouped = df.groupby(location_column)
        
        for group_key, group_df in grouped:
            if isinstance(group_key, tuple):
                location_id, time_period = group_key
                doc_id = f"{location_id}_{time_period.strftime('%Y%m%d')}"
            else:
                location_id = group_key
                doc_id = location_id
            
            # Combine all text for this location-time
            combined_text = ' '.join(group_df[text_column].astype(str))
            
            # Process the combined text
            processed_tokens = self.process_document(combined_text)
            
            if processed_tokens:
                location_docs[doc_id] = processed_tokens
        
        self.logger.info(f"Created {len(location_docs)} location documents")
        return location_docs
    
    def filter_vocabulary(
        self,
        processed_docs: List[List[str]],
        min_freq: int = 2,
        max_freq_ratio: float = 0.8
    ) -> List[List[str]]:
        """Filter vocabulary based on frequency."""
        # Count word frequencies
        word_freq = {}
        total_docs = len(processed_docs)
        
        for doc in processed_docs:
            unique_words = set(doc)
            for word in unique_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Filter words
        valid_words = set()
        for word, freq in word_freq.items():
            if min_freq <= freq <= (max_freq_ratio * total_docs):
                valid_words.add(word)
        
        # Filter documents
        filtered_docs = []
        for doc in processed_docs:
            filtered_doc = [word for word in doc if word in valid_words]
            if filtered_doc:  # Only keep non-empty documents
                filtered_docs.append(filtered_doc)
        
        self.logger.info(f"Vocabulary filtered: {len(word_freq)} -> {len(valid_words)} words")
        self.logger.info(f"Documents filtered: {len(processed_docs)} -> {len(filtered_docs)}")
        
        return filtered_docs
