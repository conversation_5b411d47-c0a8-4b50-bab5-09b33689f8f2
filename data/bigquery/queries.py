"""BigQuery SQL queries for DCMN framework."""

from typing import Dict, List, Optional
from datetime import datetime, timedelta


class DCMNQueries:
    """Collection of SQL queries for DCMN data processing."""
    
    @staticmethod
    def get_user_activity_sequences(
        user_id: str,
        start_date: datetime,
        end_date: datetime,
        min_confidence: float = 0.7
    ) -> str:
        """Get activity sequences for a specific user within date range."""
        return f"""
        SELECT 
            user_id,
            sequence_id,
            timestamp,
            latitude,
            longitude,
            activity_type,
            confidence_score,
            duration_minutes,
            smallest_circle_id,
            osm_features
        FROM `{{project_id}}.{{dataset_id}}.activity_sequences`
        WHERE user_id = '{user_id}'
            AND timestamp BETWEEN '{start_date.isoformat()}' AND '{end_date.isoformat()}'
            AND confidence_score >= {min_confidence}
        ORDER BY timestamp
        """
    
    @staticmethod
    def get_location_features_batch(circle_ids: List[str]) -> str:
        """Get location features for multiple circles."""
        circle_list = "', '".join(circle_ids)
        return f"""
        SELECT 
            smallest_circle_id,
            center_latitude,
            center_longitude,
            radius_meters,
            osm_node_count,
            osm_way_count,
            poi_density,
            residential_ratio,
            commercial_ratio,
            industrial_ratio,
            recreational_ratio,
            transportation_ratio
        FROM `{{project_id}}.{{dataset_id}}.location_features`
        WHERE smallest_circle_id IN ('{circle_list}')
        """
    
    @staticmethod
    def get_textual_data_for_lda(
        start_date: datetime,
        end_date: datetime,
        data_sources: Optional[List[str]] = None
    ) -> str:
        """Get textual data for LDA topic modeling."""
        source_filter = ""
        if data_sources:
            source_list = "', '".join(data_sources)
            source_filter = f"AND data_source IN ('{source_list}')"
        
        return f"""
        SELECT 
            document_id,
            smallest_circle_id,
            data_source,
            text_content,
            timestamp,
            language
        FROM `{{project_id}}.{{dataset_id}}.textual_data`
        WHERE timestamp BETWEEN '{start_date.isoformat()}' AND '{end_date.isoformat()}'
            {source_filter}
            AND language = 'en'
        ORDER BY smallest_circle_id, timestamp
        """
    
    @staticmethod
    def insert_topic_signatures(table_suffix: str = "") -> str:
        """Insert location topic signatures from LDA results."""
        table_name = f"location_topic_signatures{table_suffix}"
        return f"""
        INSERT INTO `{{project_id}}.{{dataset_id}}.{table_name}` 
        (smallest_circle_id, timestamp, topic_distribution, dominant_topic_id, topic_coherence)
        VALUES (?, ?, ?, ?, ?)
        """
    
    @staticmethod
    def get_topic_signatures_for_hmm(
        circle_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> str:
        """Get topic signatures for HMM regime detection."""
        return f"""
        SELECT 
            smallest_circle_id,
            timestamp,
            topic_distribution,
            dominant_topic_id,
            topic_coherence
        FROM `{{project_id}}.{{dataset_id}}.location_topic_signatures`
        WHERE smallest_circle_id = '{circle_id}'
            AND timestamp BETWEEN '{start_date.isoformat()}' AND '{end_date.isoformat()}'
        ORDER BY timestamp
        """
    
    @staticmethod
    def insert_topic_regimes(table_suffix: str = "") -> str:
        """Insert location topic regimes from HMM results."""
        table_name = f"location_topic_regimes{table_suffix}"
        return f"""
        INSERT INTO `{{project_id}}.{{dataset_id}}.{table_name}`
        (smallest_circle_id, timestamp, regime_id, regime_probability, regime_name, 
         regime_characteristics, transition_probability)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
    
    @staticmethod
    def get_familiar_locations_analysis(user_id: str, days_back: int = 30) -> str:
        """Analyze user's familiar locations for optimization."""
        cutoff_date = (datetime.now() - timedelta(days=days_back)).isoformat()
        
        return f"""
        WITH location_stats AS (
            SELECT 
                user_id,
                smallest_circle_id,
                COUNT(*) as visit_count,
                AVG(duration_minutes) as avg_duration,
                MAX(timestamp) as last_visit,
                MIN(timestamp) as first_visit,
                STDDEV(duration_minutes) as duration_std
            FROM `{{project_id}}.{{dataset_id}}.activity_sequences`
            WHERE user_id = '{user_id}'
                AND timestamp >= '{cutoff_date}'
            GROUP BY user_id, smallest_circle_id
        ),
        location_scores AS (
            SELECT 
                *,
                visit_count * LOG(avg_duration + 1) * 
                EXP(-DATE_DIFF(CURRENT_DATE(), DATE(last_visit), DAY) / 30.0) as familiarity_score
            FROM location_stats
        )
        SELECT 
            smallest_circle_id,
            visit_count,
            avg_duration,
            last_visit,
            familiarity_score,
            RANK() OVER (ORDER BY familiarity_score DESC) as rank
        FROM location_scores
        ORDER BY familiarity_score DESC
        LIMIT 50
        """
    
    @staticmethod
    def get_contextual_features(
        user_id: str,
        circle_id: str,
        timestamp: datetime,
        context_window_hours: int = 6
    ) -> str:
        """Get contextual features for CMN inference."""
        start_time = (timestamp - timedelta(hours=context_window_hours)).isoformat()
        end_time = (timestamp + timedelta(hours=context_window_hours)).isoformat()
        
        return f"""
        WITH recent_activities AS (
            SELECT 
                activity_type,
                duration_minutes,
                timestamp,
                LAG(activity_type) OVER (ORDER BY timestamp) as prev_activity,
                LEAD(activity_type) OVER (ORDER BY timestamp) as next_activity
            FROM `{{project_id}}.{{dataset_id}}.activity_sequences`
            WHERE user_id = '{user_id}'
                AND timestamp BETWEEN '{start_time}' AND '{end_time}'
            ORDER BY timestamp
        ),
        location_context AS (
            SELECT 
                lf.*,
                lts.topic_distribution,
                lts.dominant_topic_id,
                ltr.regime_id,
                ltr.regime_probability,
                ltr.regime_name
            FROM `{{project_id}}.{{dataset_id}}.location_features` lf
            LEFT JOIN `{{project_id}}.{{dataset_id}}.location_topic_signatures` lts
                ON lf.smallest_circle_id = lts.smallest_circle_id
                AND DATE(lts.timestamp) = DATE('{timestamp.isoformat()}')
            LEFT JOIN `{{project_id}}.{{dataset_id}}.location_topic_regimes` ltr
                ON lf.smallest_circle_id = ltr.smallest_circle_id
                AND DATE(ltr.timestamp) = DATE('{timestamp.isoformat()}')
            WHERE lf.smallest_circle_id = '{circle_id}'
        ),
        user_context AS (
            SELECT 
                home_latitude,
                home_longitude,
                work_latitude,
                work_longitude,
                familiar_locations
            FROM `{{project_id}}.{{dataset_id}}.user_metadata`
            WHERE user_id = '{user_id}'
        )
        SELECT 
            ra.*,
            lc.*,
            uc.*,
            EXTRACT(HOUR FROM TIMESTAMP('{timestamp.isoformat()}')) as hour_of_day,
            EXTRACT(DAYOFWEEK FROM TIMESTAMP('{timestamp.isoformat()}')) as day_of_week
        FROM recent_activities ra
        CROSS JOIN location_context lc
        CROSS JOIN user_context uc
        """
