-- Sample data for DCMN framework BigQuery tables

-- Sample activity sequences
INSERT INTO `{project_id}.{dataset_id}.activity_sequences` VALUES
-- User 1 - Typical weekday pattern
('user_001', 'seq_001_001', '2024-01-15 07:30:00', 40.7589, -73.9851, 'home', 0.95, 480, 'circle_001', STRUCT('residential', NULL, NULL, NULL, 'house', 'residential'), CURRENT_TIMESTAMP()),
('user_001', 'seq_001_002', '2024-01-15 08:45:00', 40.7505, -73.9934, 'transportation', 0.85, 25, 'circle_002', STRUCT('public_transport', NULL, NULL, NULL, NULL, 'transportation'), CURRENT_TIMESTAMP()),
('user_001', 'seq_001_003', '2024-01-15 09:15:00', 40.7614, -73.9776, 'work', 0.92, 480, 'circle_003', STRUCT(NULL, NULL, NULL, NULL, 'office', 'commercial'), CURRENT_TIMESTAMP()),
('user_001', 'seq_001_004', '2024-01-15 12:30:00', 40.7580, -73.9855, 'dining', 0.88, 45, 'circle_004', STRUCT('restaurant', NULL, NULL, NULL, 'commercial', 'commercial'), CURRENT_TIMESTAMP()),
('user_001', 'seq_001_005', '2024-01-15 18:30:00', 40.7589, -73.9851, 'home', 0.95, 720, 'circle_001', STRUCT('residential', NULL, NULL, NULL, 'house', 'residential'), CURRENT_TIMESTAMP()),

-- User 2 - Weekend pattern
('user_002', 'seq_002_001', '2024-01-13 09:00:00', 40.7282, -73.7949, 'home', 0.93, 120, 'circle_005', STRUCT('residential', NULL, NULL, NULL, 'apartment', 'residential'), CURRENT_TIMESTAMP()),
('user_002', 'seq_002_002', '2024-01-13 11:30:00', 40.7505, -73.9934, 'shopping', 0.87, 180, 'circle_006', STRUCT(NULL, 'supermarket', NULL, NULL, 'retail', 'commercial'), CURRENT_TIMESTAMP()),
('user_002', 'seq_002_003', '2024-01-13 15:00:00', 40.7829, -73.9654, 'recreation', 0.91, 240, 'circle_007', STRUCT(NULL, NULL, 'park', NULL, NULL, 'recreation'), CURRENT_TIMESTAMP()),
('user_002', 'seq_002_004', '2024-01-13 19:30:00', 40.7505, -73.9934, 'dining', 0.89, 90, 'circle_008', STRUCT('restaurant', NULL, NULL, NULL, 'commercial', 'commercial'), CURRENT_TIMESTAMP()),

-- User 3 - Mixed pattern with unfamiliar locations
('user_003', 'seq_003_001', '2024-01-16 08:00:00', 40.6892, -74.0445, 'home', 0.94, 60, 'circle_009', STRUCT('residential', NULL, NULL, NULL, 'house', 'residential'), CURRENT_TIMESTAMP()),
('user_003', 'seq_003_002', '2024-01-16 10:30:00', 40.7831, -73.9712, 'healthcare', 0.82, 120, 'circle_010', STRUCT('hospital', NULL, NULL, NULL, 'hospital', 'healthcare'), CURRENT_TIMESTAMP()),
('user_003', 'seq_003_003', '2024-01-16 14:00:00', 40.7505, -73.9934, 'other', 0.65, 45, 'circle_011', STRUCT(NULL, NULL, NULL, 'museum', 'public', 'cultural'), CURRENT_TIMESTAMP());

-- Sample location features
INSERT INTO `{project_id}.{dataset_id}.location_features` VALUES
('circle_001', 40.7589, -73.9851, 150.0, 25, 8, 0.15, 0.8, 0.1, 0.0, 0.1, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_002', 40.7505, -73.9934, 200.0, 45, 15, 0.25, 0.2, 0.3, 0.0, 0.0, 0.5, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_003', 40.7614, -73.9776, 300.0, 60, 20, 0.35, 0.1, 0.7, 0.1, 0.1, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_004', 40.7580, -73.9855, 100.0, 30, 12, 0.45, 0.2, 0.6, 0.0, 0.2, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_005', 40.7282, -73.7949, 180.0, 35, 10, 0.18, 0.75, 0.15, 0.0, 0.1, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_006', 40.7505, -73.9934, 250.0, 50, 18, 0.40, 0.3, 0.5, 0.0, 0.2, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_007', 40.7829, -73.9654, 500.0, 20, 5, 0.08, 0.1, 0.1, 0.0, 0.8, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_008', 40.7505, -73.9934, 120.0, 25, 8, 0.50, 0.2, 0.6, 0.0, 0.2, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_009', 40.6892, -74.0445, 160.0, 28, 9, 0.16, 0.8, 0.1, 0.0, 0.1, 0.0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_010', 40.7831, -73.9712, 400.0, 40, 12, 0.20, 0.3, 0.2, 0.0, 0.1, 0.4, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),
('circle_011', 40.7505, -73.9934, 200.0, 35, 10, 0.30, 0.2, 0.3, 0.0, 0.4, 0.1, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

-- Sample user metadata
INSERT INTO `{project_id}.{dataset_id}.user_metadata` VALUES
('user_001', '25-34', 'M', 'professional', 40.7589, -73.9851, 0.95, 40.7614, -73.9776, 0.92, 
 [STRUCT('circle_001', 0.95, 480, '2024-01-15 07:30:00'), 
  STRUCT('circle_003', 0.92, 480, '2024-01-15 09:15:00'),
  STRUCT('circle_004', 0.75, 45, '2024-01-15 12:30:00')], 
 CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),

('user_002', '35-44', 'F', 'service', 40.7282, -73.7949, 0.93, NULL, NULL, NULL,
 [STRUCT('circle_005', 0.93, 120, '2024-01-13 09:00:00'),
  STRUCT('circle_006', 0.80, 180, '2024-01-13 11:30:00'),
  STRUCT('circle_007', 0.85, 240, '2024-01-13 15:00:00')],
 CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP()),

('user_003', '45-54', 'F', 'healthcare', 40.6892, -74.0445, 0.94, 40.7831, -73.9712, 0.88,
 [STRUCT('circle_009', 0.94, 60, '2024-01-16 08:00:00'),
  STRUCT('circle_010', 0.70, 120, '2024-01-16 10:30:00')],
 CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP());

-- Sample textual data for LDA
INSERT INTO `{project_id}.{dataset_id}.textual_data` VALUES
('doc_001', 'circle_001', 'osm_tags', 'residential house apartment building home family neighborhood quiet suburban', '2024-01-15 00:00:00', 'en', 0.2, NULL, CURRENT_TIMESTAMP()),
('doc_002', 'circle_002', 'osm_tags', 'subway station metro public transport transit commute underground rail', '2024-01-15 00:00:00', 'en', 0.0, NULL, CURRENT_TIMESTAMP()),
('doc_003', 'circle_003', 'osm_tags', 'office building business corporate work professional commercial tower', '2024-01-15 00:00:00', 'en', 0.1, NULL, CURRENT_TIMESTAMP()),
('doc_004', 'circle_004', 'osm_tags', 'restaurant food dining cuisine meal lunch dinner cafe bistro', '2024-01-15 00:00:00', 'en', 0.6, NULL, CURRENT_TIMESTAMP()),
('doc_005', 'circle_006', 'osm_tags', 'supermarket grocery shopping retail store market food supplies', '2024-01-13 00:00:00', 'en', 0.3, NULL, CURRENT_TIMESTAMP()),
('doc_006', 'circle_007', 'osm_tags', 'park recreation outdoor nature green space leisure walking jogging', '2024-01-13 00:00:00', 'en', 0.8, NULL, CURRENT_TIMESTAMP()),
('doc_007', 'circle_010', 'osm_tags', 'hospital healthcare medical clinic doctor treatment emergency health', '2024-01-16 00:00:00', 'en', -0.1, NULL, CURRENT_TIMESTAMP()),
('doc_008', 'circle_011', 'osm_tags', 'museum culture art history exhibition gallery education tourism', '2024-01-16 00:00:00', 'en', 0.7, NULL, CURRENT_TIMESTAMP());
