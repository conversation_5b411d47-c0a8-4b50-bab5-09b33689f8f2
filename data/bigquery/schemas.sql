-- BigQuery table schemas for DCMN framework

-- Activity sequences table
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.activity_sequences` (
    user_id STRING NOT NULL,
    sequence_id STRING NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    latitude FLOAT64 NOT NULL,
    longitude FLOAT64 NOT NULL,
    activity_type STRING,
    confidence_score FLOAT64,
    duration_minutes INT64,
    smallest_circle_id STRING,
    osm_features STRUCT<
        amenity STRING,
        shop STRING,
        leisure STRING,
        tourism STRING,
        building STRING,
        landuse STRING
    >,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY DATE(timestamp)
CLUSTER BY user_id, smallest_circle_id;

-- Location features table
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.location_features` (
    smallest_circle_id STRING NOT NULL,
    center_latitude FLOAT64 NOT NULL,
    center_longitude FLOAT64 NOT NULL,
    radius_meters FLOAT64 NOT NULL,
    osm_node_count INT64,
    osm_way_count INT64,
    poi_density FLOAT64,
    residential_ratio FLOAT64,
    commercial_ratio FLOAT64,
    industrial_ratio FLOAT64,
    recreational_ratio FLOAT64,
    transportation_ratio FLOAT64,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
CLUSTER BY smallest_circle_id;

-- User metadata table
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.user_metadata` (
    user_id STRING NOT NULL,
    age_group STRING,
    gender STRING,
    occupation_category STRING,
    home_latitude FLOAT64,
    home_longitude FLOAT64,
    home_confidence FLOAT64,
    work_latitude FLOAT64,
    work_longitude FLOAT64,
    work_confidence FLOAT64,
    familiar_locations ARRAY<STRUCT<
        smallest_circle_id STRING,
        visit_frequency FLOAT64,
        avg_duration_minutes FLOAT64,
        last_visit TIMESTAMP
    >>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
CLUSTER BY user_id;

-- OSM data table
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.osm_data` (
    osm_id STRING NOT NULL,
    osm_type STRING NOT NULL, -- node, way, relation
    smallest_circle_id STRING,
    latitude FLOAT64,
    longitude FLOAT64,
    tags STRUCT<
        name STRING,
        amenity STRING,
        shop STRING,
        leisure STRING,
        tourism STRING,
        building STRING,
        landuse STRING,
        highway STRING,
        public_transport STRING,
        cuisine STRING,
        opening_hours STRING
    >,
    geometry STRING, -- WKT format
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
CLUSTER BY smallest_circle_id, osm_type;

-- Textual data table for LDA
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.textual_data` (
    document_id STRING NOT NULL,
    smallest_circle_id STRING NOT NULL,
    data_source STRING NOT NULL, -- osm_tags, social_media, reviews, events
    text_content STRING NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    language STRING DEFAULT 'en',
    sentiment_score FLOAT64,
    topic_weights ARRAY<STRUCT<
        topic_id INT64,
        weight FLOAT64
    >>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY DATE(timestamp)
CLUSTER BY smallest_circle_id, data_source;

-- Location topic signatures table (LDA output)
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.location_topic_signatures` (
    smallest_circle_id STRING NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    topic_distribution ARRAY<STRUCT<
        topic_id INT64,
        probability FLOAT64,
        topic_words ARRAY<STRING>
    >>,
    dominant_topic_id INT64,
    topic_coherence FLOAT64,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY DATE(timestamp)
CLUSTER BY smallest_circle_id;

-- Location topic regimes table (HMM output)
CREATE TABLE IF NOT EXISTS `{project_id}.{dataset_id}.location_topic_regimes` (
    smallest_circle_id STRING NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    regime_id INT64 NOT NULL,
    regime_probability FLOAT64 NOT NULL,
    regime_name STRING,
    regime_characteristics STRUCT<
        business_hours_weight FLOAT64,
        weekend_weight FLOAT64,
        event_weight FLOAT64,
        quiet_weight FLOAT64
    >,
    transition_probability FLOAT64,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY DATE(timestamp)
CLUSTER BY smallest_circle_id, regime_id;
