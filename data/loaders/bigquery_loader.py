"""BigQuery data loader for DCMN framework."""

import logging
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

from config.bigquery_config import BigQueryConfig
from data.bigquery.queries import DCMNQueries


class BigQueryLoader:
    """Data loader for BigQuery operations."""
    
    def __init__(self, config: BigQueryConfig):
        """Initialize BigQuery loader."""
        self.config = config
        self.config.validate()
        
        # Initialize BigQuery client
        if config.credentials_path:
            self.client = bigquery.Client.from_service_account_json(
                config.credentials_path,
                project=config.project_id
            )
        else:
            self.client = bigquery.Client(project=config.project_id)
        
        self.queries = DCMNQueries()
        self.logger = logging.getLogger(__name__)
    
    def create_dataset_if_not_exists(self) -> None:
        """Create dataset if it doesn't exist."""
        dataset_id = f"{self.config.project_id}.{self.config.dataset_id}"
        
        try:
            self.client.get_dataset(dataset_id)
            self.logger.info(f"Dataset {dataset_id} already exists")
        except NotFound:
            dataset = bigquery.Dataset(dataset_id)
            dataset.location = self.config.location
            dataset = self.client.create_dataset(dataset, timeout=30)
            self.logger.info(f"Created dataset {dataset_id}")
    
    def execute_query(self, query: str, **format_kwargs) -> pd.DataFrame:
        """Execute a SQL query and return results as DataFrame."""
        # Format query with config parameters
        formatted_query = query.format(
            project_id=self.config.project_id,
            dataset_id=self.config.dataset_id,
            **format_kwargs
        )
        
        self.logger.debug(f"Executing query: {formatted_query[:200]}...")
        
        try:
            query_job = self.client.query(formatted_query)
            results = query_job.result()
            df = results.to_dataframe()
            
            self.logger.info(f"Query executed successfully, returned {len(df)} rows")
            return df
            
        except Exception as e:
            self.logger.error(f"Query execution failed: {str(e)}")
            raise
    
    def load_user_activity_sequences(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime,
        min_confidence: float = 0.7
    ) -> pd.DataFrame:
        """Load activity sequences for a specific user."""
        query = self.queries.get_user_activity_sequences(
            user_id, start_date, end_date, min_confidence
        )
        return self.execute_query(query)
    
    def load_location_features(self, circle_ids: List[str]) -> pd.DataFrame:
        """Load location features for multiple circles."""
        if not circle_ids:
            return pd.DataFrame()
        
        query = self.queries.get_location_features_batch(circle_ids)
        return self.execute_query(query)
    
    def load_textual_data_for_lda(
        self,
        start_date: datetime,
        end_date: datetime,
        data_sources: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Load textual data for LDA topic modeling."""
        query = self.queries.get_textual_data_for_lda(
            start_date, end_date, data_sources
        )
        return self.execute_query(query)
    
    def load_topic_signatures_for_hmm(
        self,
        circle_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Load topic signatures for HMM regime detection."""
        query = self.queries.get_topic_signatures_for_hmm(
            circle_id, start_date, end_date
        )
        return self.execute_query(query)
    
    def load_familiar_locations_analysis(
        self,
        user_id: str,
        days_back: int = 30
    ) -> pd.DataFrame:
        """Load familiar locations analysis for a user."""
        query = self.queries.get_familiar_locations_analysis(user_id, days_back)
        return self.execute_query(query)
    
    def load_contextual_features(
        self,
        user_id: str,
        circle_id: str,
        timestamp: datetime,
        context_window_hours: int = 6
    ) -> pd.DataFrame:
        """Load contextual features for CMN inference."""
        query = self.queries.get_contextual_features(
            user_id, circle_id, timestamp, context_window_hours
        )
        return self.execute_query(query)
    
    def insert_dataframe(
        self,
        df: pd.DataFrame,
        table_name: str,
        write_disposition: str = "WRITE_APPEND"
    ) -> None:
        """Insert DataFrame into BigQuery table."""
        table_id = self.config.get_table_id(table_name)
        
        job_config = bigquery.LoadJobConfig(
            write_disposition=write_disposition,
            autodetect=True
        )
        
        try:
            job = self.client.load_table_from_dataframe(
                df, table_id, job_config=job_config
            )
            job.result()  # Wait for the job to complete
            
            self.logger.info(f"Inserted {len(df)} rows into {table_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to insert data into {table_id}: {str(e)}")
            raise
    
    def insert_topic_signatures(
        self,
        signatures_data: List[Dict[str, Any]],
        table_suffix: str = ""
    ) -> None:
        """Insert topic signatures data."""
        df = pd.DataFrame(signatures_data)
        table_name = f"location_topic_signatures{table_suffix}"
        self.insert_dataframe(df, table_name)
    
    def insert_topic_regimes(
        self,
        regimes_data: List[Dict[str, Any]],
        table_suffix: str = ""
    ) -> None:
        """Insert topic regimes data."""
        df = pd.DataFrame(regimes_data)
        table_name = f"location_topic_regimes{table_suffix}"
        self.insert_dataframe(df, table_name)
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get information about a table."""
        table_id = self.config.get_table_id(table_name)
        
        try:
            table = self.client.get_table(table_id)
            return {
                "table_id": table_id,
                "num_rows": table.num_rows,
                "num_bytes": table.num_bytes,
                "created": table.created,
                "modified": table.modified,
                "schema": [{"name": field.name, "type": field.field_type} 
                          for field in table.schema]
            }
        except NotFound:
            return {"error": f"Table {table_id} not found"}
    
    def close(self) -> None:
        """Close BigQuery client connection."""
        if hasattr(self.client, 'close'):
            self.client.close()
        self.logger.info("BigQuery client connection closed")
